version: '3.8'

services:
  agentic-framework:
    build: .
    container_name: agentic-framework
    ports:
      - "8080:8080"  # REST API
      - "8081:8081"  # MCP Server
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xmx2g -Xms1g
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - agentic-network

  # Optional: Add Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: agentic-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis-data:/data
  #   restart: unless-stopped
  #   networks:
  #     - agentic-network

networks:
  agentic-network:
    driver: bridge

volumes:
  # redis-data:
  logs:
  data:
