2025-08-11 12:35:34 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - Starting AgenticFrameworkApplication using Java 17.0.15 with PID 10776 (/home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy)/agentic-framework/target/classes started by perennial in /home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy))
2025-08-11 12:35:34 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - The following 1 profile is active: "dev"
2025-08-11 12:35:34 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-11 12:35:34 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-11 12:35:36 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 12:35:36 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 12:35:36 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-11 12:35:36 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-11 12:35:36 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1651 ms
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Initializing Agent Registry...
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: General Purpose Agent (general-purpose-agent)
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Time Series Forecasting Agent (time-series-forecasting-agent)
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Software Bug Assistant Agent (software-bug-assistant-agent)
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Data Analysis Agent (data-analysis-agent)
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Code Review Agent (code-review-agent)
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry initialized with 5 agents
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-5
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4o
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1-mini
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-3.5
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-sonnet-20241022
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-haiku-20241022
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-opus-latest
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-sonnet-20240229
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-haiku-20240307
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-opus-4-1-20250805
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: llama2
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: mistral
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Model registry initialized with 13 models
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.mcp.ContextManager - Context cleanup task started with interval: 3600000 ms
2025-08-11 12:35:36 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - MCP Server started successfully on localhost:8081
2025-08-11 12:35:36 [restartedMain] ERROR c.a.framework.mcp.AgenticMcpServer - Failed to start Agentic MCP Server
java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.Net.bind(Net.java:544)
	at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:167)
	at com.agentic.framework.mcp.AgenticMcpServer.initializeServer(AgenticMcpServer.java:76)
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:421)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1767)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.agentic.framework.AgenticFrameworkApplication.main(AgenticFrameworkApplication.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-08-11 12:35:36 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'agenticMcpServer': Invocation of init method failed
2025-08-11 12:35:36 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - Shutting down MCP Server...
2025-08-11 12:35:36 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - MCP Server shutdown complete
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Cleaning up Agent Registry...
2025-08-11 12:35:36 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry cleanup complete
2025-08-11 12:35:36 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-11 12:35:36 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-11 12:35:36 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'agenticMcpServer': Invocation of init method failed
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:421)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1767)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.agentic.framework.AgenticFrameworkApplication.main(AgenticFrameworkApplication.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: java.lang.RuntimeException: Failed to start Agentic MCP Server
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	... 23 common frames omitted
Caused by: java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.Net.bind(Net.java:544)
	at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:167)
	at com.agentic.framework.mcp.AgenticMcpServer.initializeServer(AgenticMcpServer.java:76)
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:59)
	... 30 common frames omitted
2025-08-11 12:53:15 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - Starting AgenticFrameworkApplication using Java 17.0.15 with PID 32131 (/home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy)/agentic-framework/target/classes started by perennial in /home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy))
2025-08-11 12:53:15 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - The following 1 profile is active: "dev"
2025-08-11 12:53:15 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-11 12:53:15 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-11 12:53:16 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 12:53:16 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 12:53:16 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-11 12:53:16 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-11 12:53:16 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1730 ms
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Initializing Agent Registry...
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: General Purpose Agent (general-purpose-agent)
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Time Series Forecasting Agent (time-series-forecasting-agent)
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Software Bug Assistant Agent (software-bug-assistant-agent)
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Data Analysis Agent (data-analysis-agent)
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Code Review Agent (code-review-agent)
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry initialized with 5 agents
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-5
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4o
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1-mini
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-3.5
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-sonnet-20241022
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-haiku-20241022
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-opus-latest
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-sonnet-20240229
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-haiku-20240307
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-opus-4-1-20250805
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: llama2
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: mistral
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Model registry initialized with 13 models
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.mcp.ContextManager - Context cleanup task started with interval: 3600000 ms
2025-08-11 12:53:17 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - MCP Server started successfully on localhost:8081
2025-08-11 12:53:17 [restartedMain] ERROR c.a.framework.mcp.AgenticMcpServer - Failed to start Agentic MCP Server
java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.Net.bind(Net.java:544)
	at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:167)
	at com.agentic.framework.mcp.AgenticMcpServer.initializeServer(AgenticMcpServer.java:76)
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:59)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:421)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1767)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.agentic.framework.AgenticFrameworkApplication.main(AgenticFrameworkApplication.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
2025-08-11 12:53:17 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'agenticMcpServer': Invocation of init method failed
2025-08-11 12:53:17 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - Shutting down MCP Server...
2025-08-11 12:53:17 [restartedMain] INFO  com.agentic.framework.mcp.McpServer - MCP Server shutdown complete
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Cleaning up Agent Registry...
2025-08-11 12:53:17 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry cleanup complete
2025-08-11 12:53:17 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-11 12:53:17 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-11 12:53:17 [restartedMain] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'agenticMcpServer': Invocation of init method failed
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:421)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1767)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331)
	at com.agentic.framework.AgenticFrameworkApplication.main(AgenticFrameworkApplication.java:16)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50)
Caused by: java.lang.RuntimeException: Failed to start Agentic MCP Server
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:66)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	... 23 common frames omitted
Caused by: java.net.BindException: Address already in use
	at java.base/sun.nio.ch.Net.bind0(Native Method)
	at java.base/sun.nio.ch.Net.bind(Net.java:555)
	at java.base/sun.nio.ch.Net.bind(Net.java:544)
	at java.base/sun.nio.ch.NioSocketImpl.bind(NioSocketImpl.java:648)
	at java.base/java.net.ServerSocket.bind(ServerSocket.java:388)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:274)
	at java.base/java.net.ServerSocket.<init>(ServerSocket.java:167)
	at com.agentic.framework.mcp.AgenticMcpServer.initializeServer(AgenticMcpServer.java:76)
	at com.agentic.framework.mcp.AgenticMcpServer.start(AgenticMcpServer.java:59)
	... 30 common frames omitted
2025-08-11 13:21:36 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - Starting AgenticFrameworkApplication using Java 17.0.15 with PID 54960 (/home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy)/agentic-framework/target/classes started by perennial in /home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy))
2025-08-11 13:21:36 [restartedMain] INFO  c.a.f.AgenticFrameworkApplication - The following 1 profile is active: "dev"
2025-08-11 13:21:36 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-11 13:21:36 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-11 13:21:38 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-11 13:21:38 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-11 13:21:38 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-11 13:21:38 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-11 13:21:38 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2134 ms
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Initializing Agent Registry...
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: General Purpose Agent (general-purpose-agent)
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Time Series Forecasting Agent (time-series-forecasting-agent)
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Software Bug Assistant Agent (software-bug-assistant-agent)
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Data Analysis Agent (data-analysis-agent)
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Registered agent: Code Review Agent (code-review-agent)
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry initialized with 5 agents
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-5
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4o
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-4.1-mini
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: gpt-3.5
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-sonnet-20241022
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-5-haiku-20241022
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-opus-latest
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-sonnet-20240229
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-3-haiku-20240307
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: claude-opus-4-1-20250805
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: llama2
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Registered model: mistral
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ModelRegistry - Model registry initialized with 13 models
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.mcp.ContextManager - Context cleanup task started with interval: 3600000 ms
2025-08-11 13:21:39 [restartedMain] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'modelController' defined in file [/home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy)/agentic-framework/target/classes/com/agentic/framework/controller/ModelController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'modelService' defined in file [/home/<USER>/Desktop/AI_DEMO/Agentic_new (another copy)/agentic-framework/target/classes/com/agentic/framework/service/ModelService.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.agentic.framework.mcp.McpServer' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Cleaning up Agent Registry...
2025-08-11 13:21:39 [restartedMain] INFO  c.a.framework.agent.AgentRegistry - Agent Registry cleanup complete
2025-08-11 13:21:39 [restartedMain] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-11 13:21:39 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-11 13:21:39 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 2 of constructor in com.agentic.framework.service.ModelService required a bean of type 'com.agentic.framework.mcp.McpServer' that could not be found.


Action:

Consider defining a bean of type 'com.agentic.framework.mcp.McpServer' in your configuration.

