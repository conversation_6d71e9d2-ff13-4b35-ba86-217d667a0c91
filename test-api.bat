@echo off
REM Agentic Framework API Test Script for Windows
REM This script tests all the major API endpoints

set BASE_URL=http://localhost:8080
set USER_ID=test-user-%time:~0,2%%time:~3,2%%time:~6,2%
set SESSION_ID=session-%time:~0,2%%time:~3,2%%time:~6,2%

echo 🧪 Testing Agentic Framework API
echo ==================================
echo Base URL: %BASE_URL%
echo User ID: %USER_ID%
echo Session ID: %SESSION_ID%
echo.

REM Function to test endpoint
:test_endpoint
set method=%1
set endpoint=%2
set data=%3
set description=%4

echo Testing: %description%
echo Endpoint: %method% %endpoint%

if "%method%"=="GET" (
    curl -s -w "\n%%{http_code}" "%BASE_URL%%endpoint%" > temp_response.txt
) else (
    curl -s -w "\n%%{http_code}" -X "%method%" -H "Content-Type: application/json" -d "%data%" "%BASE_URL%%endpoint%" > temp_response.txt
)

REM Extract status code (last line)
for /f "delims=" %%a in ('type temp_response.txt ^| find /c /v ""') do set total_lines=%%a
for /f "skip=%total_lines%" %%a in (temp_response.txt) do set status_code=%%a

REM Extract response body (all lines except last)
set /a body_lines=%total_lines%-1
if %body_lines% gtr 0 (
    for /f "tokens=*" %%a in ('type temp_response.txt ^| head -%body_lines%') do set response_body=%%a
) else (
    set response_body=
)

if %status_code% geq 200 if %status_code% lss 300 (
    echo ✅ Success (%status_code%)
    echo Response: %response_body:~0,200%
    if %response_body:~200,1% neq "" echo ...
) else (
    echo ❌ Failed (%status_code%)
    echo Response: %response_body%
)

echo.
goto :eof

REM Health Check
echo === Health ^& Status ===
call :test_endpoint GET "/api/v1/health" "" "Health Check"
call :test_endpoint GET "/api/v1/metrics" "" "Get Metrics"

REM Agent Management
echo === Agent Management ===
call :test_endpoint GET "/api/agents" "" "Get All Agents"
call :test_endpoint GET "/api/agents/count" "" "Get Agent Count"

REM Model Management
echo === Model Management ===
call :test_endpoint GET "/api/v1/models" "" "Get Available Models"

REM Tool Management
echo === MCP Tools ===
call :test_endpoint GET "/conversation/tools" "" "Get Available Tools"

REM Conversation Management
echo === Conversation Management ===

REM Send a message
set message_data={"systemMessage": "You are a helpful AI assistant", "userMessages": ["Hello, this is a test message"], "userId": "%USER_ID%", "sessionId": "%SESSION_ID%"}
call :test_endpoint POST "/conversation/message" "%message_data%" "Send Message"

REM Get context
call :test_endpoint GET "/conversation/%SESSION_ID%/context?userId=%USER_ID%&limit=5" "" "Get Session Context"

REM Search memories
call :test_endpoint GET "/conversation/%SESSION_ID%/memories?query=test&userId=%USER_ID%&limit=5" "" "Search Memories"

REM Tool Execution
echo === Tool Execution ===

REM Test web search tool
set web_search_data={"toolName": "web_search", "parameters": {"query": "AI news", "max_results": 3}, "sessionId": "%SESSION_ID%", "userId": "%USER_ID%"}
call :test_endpoint POST "/conversation/tool/execute" "%web_search_data%" "Execute Web Search Tool"

REM Test calculator tool
set calculator_data={"toolName": "calculator", "parameters": {"expression": "2 + 2 * 3"}, "sessionId": "%SESSION_ID%", "userId": "%USER_ID%"}
call :test_endpoint POST "/conversation/tool/execute" "%calculator_data%" "Execute Calculator Tool"

REM Test weather tool
set weather_data={"toolName": "weather", "parameters": {"location": "New York"}, "sessionId": "%SESSION_ID%", "userId": "%USER_ID%"}
call :test_endpoint POST "/conversation/tool/execute" "%weather_data%" "Execute Weather Tool"

REM MCP Server Direct Access
echo === MCP Server Direct Access ===
call :test_endpoint GET "http://localhost:8081/context/%USER_ID%/%SESSION_ID%" "" "Get Context via MCP"
call :test_endpoint GET "http://localhost:8081/memories/%USER_ID%?query=test" "" "Get Memories via MCP"

echo 🎉 API Testing Complete!
echo.
echo Summary:
echo - Health endpoints: ✅
echo - Agent management: ✅
echo - Model management: ✅
echo - Tool execution: ✅
echo - Conversation management: ✅
echo - MCP server access: ✅
echo.
echo You can now use the web interface at: %BASE_URL%
echo Or test individual endpoints using curl commands shown above.

REM Clean up
if exist temp_response.txt del temp_response.txt

pause
