#!/bin/bash

# View Contexts and Memories Script
# This script allows you to view stored contexts and memories

BASE_URL="http://localhost:8080"
MCP_URL="http://localhost:8081"

echo "🔍 Agentic Framework - Context & Memory Viewer"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to display JSON in a formatted way
display_json() {
    local json="$1"
    if command -v jq &> /dev/null; then
        echo "$json" | jq '.'
    else
        echo "$json" | python3 -m json.tool 2>/dev/null || echo "$json"
    fi
}

# Function to view context for a user/session
view_context() {
    local user_id="$1"
    local session_id="$2"
    
    echo -e "${BLUE}📋 Viewing Context${NC}"
    echo "User ID: $user_id"
    echo "Session ID: $session_id"
    echo ""
    
    # Try via main API first
    echo -e "${YELLOW}Via Main API:${NC}"
    response=$(curl -s "$BASE_URL/conversation/$session_id/context?userId=$user_id&limit=20")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to get context via main API${NC}"
    fi
    echo ""
    
    # Try via MCP server
    echo -e "${YELLOW}Via MCP Server:${NC}"
    response=$(curl -s "$MCP_URL/context/$user_id/$session_id")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to get context via MCP server${NC}"
    fi
    echo ""
}

# Function to search memories
search_memories() {
    local user_id="$1"
    local query="$2"
    
    echo -e "${BLUE}🔍 Searching Memories${NC}"
    echo "User ID: $user_id"
    echo "Query: $query"
    echo ""
    
    # Try via main API first
    echo -e "${YELLOW}Via Main API:${NC}"
    response=$(curl -s "$BASE_URL/conversation/session-123/memories?query=$query&userId=$user_id&limit=10")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to search memories via main API${NC}"
    fi
    echo ""
    
    # Try via MCP server
    echo -e "${YELLOW}Via MCP Server:${NC}"
    response=$(curl -s "$MCP_URL/memories/$user_id?query=$query")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to search memories via MCP server${NC}"
    fi
    echo ""
}

# Function to list all available tools
list_tools() {
    echo -e "${BLUE}🛠️  Available MCP Tools${NC}"
    echo ""
    
    response=$(curl -s "$BASE_URL/conversation/tools")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to get available tools${NC}"
    fi
    echo ""
}

# Function to show system status
show_status() {
    echo -e "${BLUE}📊 System Status${NC}"
    echo ""
    
    # Health check
    echo -e "${YELLOW}Health Check:${NC}"
    response=$(curl -s "$BASE_URL/api/v1/health")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Health check failed${NC}"
    fi
    echo ""
    
    # Metrics
    echo -e "${YELLOW}Metrics:${NC}"
    response=$(curl -s "$BASE_URL/api/v1/metrics")
    if [ $? -eq 0 ]; then
        display_json "$response"
    else
        echo -e "${RED}Failed to get metrics${NC}"
    fi
    echo ""
}

# Main menu
show_menu() {
    echo -e "${CYAN}Choose an option:${NC}"
    echo "1. View context for specific user/session"
    echo "2. Search memories for a user"
    echo "3. List available MCP tools"
    echo "4. Show system status"
    echo "5. View recent test data"
    echo "6. Exit"
    echo ""
}

# Interactive mode
interactive_mode() {
    while true; do
        show_menu
        read -p "Enter your choice (1-6): " choice
        
        case $choice in
            1)
                read -p "Enter User ID: " user_id
                read -p "Enter Session ID: " session_id
                view_context "$user_id" "$session_id"
                ;;
            2)
                read -p "Enter User ID: " user_id
                read -p "Enter search query: " query
                search_memories "$user_id" "$query"
                ;;
            3)
                list_tools
                ;;
            4)
                show_status
                ;;
            5)
                echo -e "${BLUE}📋 Recent Test Data${NC}"
                echo ""
                # Show some example data
                view_context "test-user-123" "session-456"
                search_memories "test-user-123" "hello"
                ;;
            6)
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}Invalid choice. Please try again.${NC}"
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
        echo ""
    done
}

# Check if arguments are provided
if [ $# -eq 0 ]; then
    # Interactive mode
    interactive_mode
else
    # Command line mode
    case "$1" in
        "context")
            if [ $# -lt 3 ]; then
                echo "Usage: $0 context <user_id> <session_id>"
                exit 1
            fi
            view_context "$2" "$3"
            ;;
        "memories")
            if [ $# -lt 3 ]; then
                echo "Usage: $0 memories <user_id> <query>"
                exit 1
            fi
            search_memories "$2" "$3"
            ;;
        "tools")
            list_tools
            ;;
        "status")
            show_status
            ;;
        "test")
            echo -e "${BLUE}📋 Test Data${NC}"
            echo ""
            view_context "test-user-123" "session-456"
            search_memories "test-user-123" "hello"
            ;;
        *)
            echo "Usage:"
            echo "  $0                    # Interactive mode"
            echo "  $0 context <user> <session>  # View context"
            echo "  $0 memories <user> <query>   # Search memories"
            echo "  $0 tools              # List tools"
            echo "  $0 status             # Show status"
            echo "  $0 test               # Show test data"
            exit 1
            ;;
    esac
fi