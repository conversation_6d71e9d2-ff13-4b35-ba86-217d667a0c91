#!/bin/bash

# Agentic Framework Startup Script
# This script starts the Agentic Framework application

echo "🚀 Starting Agentic Framework"
echo "============================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Java is installed
if ! command -v java &> /dev/null; then
    echo -e "${RED}❌ Java is not installed or not in PATH${NC}"
    echo "Please install Java 17 or higher and try again."
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 17 ]; then
    echo -e "${RED}❌ Java version $JAVA_VERSION is too old${NC}"
    echo "Please install Java 17 or higher and try again."
    exit 1
fi

echo -e "${GREEN}✅ Java $JAVA_VERSION found${NC}"

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo -e "${RED}❌ Maven is not installed or not in PATH${NC}"
    echo "Please install Maven and try again."
    exit 1
fi

echo -e "${GREEN}✅ Maven found${NC}"

# Check if the application is already running
if curl -s http://localhost:8080/api/v1/health > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Application is already running on port 8080${NC}"
    echo "You can access it at: http://localhost:8080"
    echo ""
    echo "To stop the application, press Ctrl+C"
    echo "To restart, first stop the current instance and run this script again."
    exit 0
fi

# Build the application
echo -e "${BLUE}🔨 Building application...${NC}"
if ! mvn clean install -DskipTests; then
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build successful${NC}"

# Set environment variables (optional)
export OPENAI_API_KEY=${OPENAI_API_KEY:-""}
export ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-""}

if [ -z "$OPENAI_API_KEY" ] || [ -z "$ANTHROPIC_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: API keys not set${NC}"
    echo "For full functionality, set the following environment variables:"
    echo "  export OPENAI_API_KEY=\"your-openai-api-key\""
    echo "  export ANTHROPIC_API_KEY=\"your-anthropic-api-key\""
    echo ""
    echo "The application will still start but some features may be limited."
fi

# Start the application
echo -e "${BLUE}🚀 Starting Agentic Framework...${NC}"
echo ""

# Start the application in the background
nohup java -jar target/agentic-framework-1.0.0.jar > logs/startup.log 2>&1 &
APP_PID=$!

# Wait for the application to start
echo -e "${BLUE}⏳ Waiting for application to start...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:8080/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Application started successfully!${NC}"
        break
    fi
    
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Application failed to start within 30 seconds${NC}"
        echo "Check the logs at logs/startup.log for more information."
        kill $APP_PID 2>/dev/null
        exit 1
    fi
    
    echo -n "."
    sleep 1
done

echo ""
echo -e "${GREEN}🎉 Agentic Framework is now running!${NC}"
echo ""
echo "📋 Access Information:"
echo "  • Web Interface: http://localhost:8080"
echo "  • REST API: http://localhost:8080/api/v1"
echo "  • MCP Server: http://localhost:8081"
echo "  • Health Check: http://localhost:8080/api/v1/health"
echo ""
echo "🛠️  Available Scripts:"
echo "  • Test API: ./test-api.sh"
echo "  • Test Agents: ./test-agents.sh"
echo "  • View Contexts: ./view-contexts.sh"
echo ""
echo "📚 Documentation:"
echo "  • README.md - Complete API documentation"
echo "  • API Examples - See README.md for curl examples"
echo ""
echo "🔄 To stop the application:"
echo "  • Press Ctrl+C in this terminal"
echo "  • Or run: pkill -f agentic-framework"
echo ""
echo "📊 Application Status:"
curl -s http://localhost:8080/api/v1/health | python3 -m json.tool 2>/dev/null || echo "Health check response received"

echo ""
echo -e "${BLUE}💡 Tip: Open http://localhost:8080 in your browser for the web interface!${NC}"

# Keep the script running and handle shutdown
trap 'echo ""; echo -e "${YELLOW}🛑 Shutting down Agentic Framework...${NC}"; kill $APP_PID 2>/dev/null; echo -e "${GREEN}✅ Application stopped${NC}"; exit 0' INT TERM

# Wait for the application process
wait $APP_PID
