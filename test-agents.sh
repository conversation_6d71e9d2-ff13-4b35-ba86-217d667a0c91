#!/bin/bash

# Agentic Framework - Agent Testing Script
# This script tests various agent capabilities and interactions

BASE_URL="http://localhost:8080"
USER_ID="test-user-$(date +%s)"
SESSION_ID="session-$(date +%s)"

echo "🤖 Testing Agentic Framework Agents"
echo "==================================="
echo "Base URL: $BASE_URL"
echo "User ID: $USER_ID"
echo "Session ID: $SESSION_ID"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success ($status_code)${NC}"
        echo "Response: $response_body" | head -c 300
        if [ ${#response_body} -gt 300 ]; then
            echo "..."
        fi
    else
        echo -e "${RED}❌ Failed ($status_code)${NC}"
        echo "Response: $response_body"
    fi
    echo ""
}

# Function to test agent with specific request
test_agent_request() {
    local agent_id=$1
    local system_message=$2
    local user_message=$3
    local description=$4
    
    local data='{
        "systemMessage": "'$system_message'",
        "userMessages": ["'$user_message'"],
        "agentId": "'$agent_id'",
        "userId": "'$USER_ID'",
        "sessionId": "'$SESSION_ID'"
    }'
    
    test_endpoint "POST" "/conversation/message" "$data" "$description"
}

# Agent Discovery
echo -e "${YELLOW}=== Agent Discovery ===${NC}"
test_endpoint "GET" "/api/agents" "" "Get All Available Agents"
test_endpoint "GET" "/api/agents/count" "" "Get Agent Count"

# Test different types of requests to see which agents are selected
echo -e "${YELLOW}=== Agent Selection Tests ===${NC}"

# Test 1: General conversation (should auto-select general purpose agent)
general_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["Hello, how are you today?"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$general_data" "General Conversation (Auto-select)"

# Test 2: Code review request
code_review_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["Please review this code for best practices and potential bugs"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$code_review_data" "Code Review Request (Auto-select)"

# Test 3: Data analysis request
data_analysis_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["I need help analyzing this dataset and finding patterns"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$data_analysis_data" "Data Analysis Request (Auto-select)"

# Test 4: Time series forecasting request
forecast_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["I need to forecast sales data for the next quarter"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$forecast_data" "Time Series Forecasting Request (Auto-select)"

# Test 5: Bug fixing request
bug_fix_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["I have a bug in my code that causes a null pointer exception"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$bug_fix_data" "Bug Fixing Request (Auto-select)"

# Test specific agents (if they exist)
echo -e "${YELLOW}=== Specific Agent Tests ===${NC}"

# Test General Purpose Agent
test_agent_request "general-purpose" "You are a helpful AI assistant" "Tell me about artificial intelligence" "General Purpose Agent"

# Test Code Review Agent
test_agent_request "code-review" "You are a code review expert" "Review this Python function for best practices" "Code Review Agent"

# Test Data Analysis Agent
test_agent_request "data-analysis" "You are a data analysis expert" "Help me analyze this sales dataset" "Data Analysis Agent"

# Test Time Series Forecasting Agent
test_agent_request "time-series-forecasting" "You are a time series forecasting expert" "Forecast monthly sales for the next year" "Time Series Forecasting Agent"

# Test Software Bug Assistant Agent
test_agent_request "software-bug-assistant" "You are a software debugging expert" "Help me fix this null pointer exception" "Software Bug Assistant Agent"

# Agent Capability Tests
echo -e "${YELLOW}=== Agent Capability Tests ===${NC}"

# Test agent with complex reasoning
complex_reasoning_data='{
    "systemMessage": "You are an AI assistant with strong reasoning capabilities",
    "userMessages": ["If a train leaves station A at 2 PM traveling 60 mph and another train leaves station B at 3 PM traveling 80 mph, when will they meet if the stations are 200 miles apart?"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$complex_reasoning_data" "Complex Reasoning Test"

# Test agent with creative writing
creative_writing_data='{
    "systemMessage": "You are a creative writing assistant",
    "userMessages": ["Write a short story about a robot learning to paint"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$creative_writing_data" "Creative Writing Test"

# Test agent with technical explanation
technical_explanation_data='{
    "systemMessage": "You are a technical expert",
    "userMessages": ["Explain how machine learning algorithms work in simple terms"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$technical_explanation_data" "Technical Explanation Test"

# Test agent with problem solving
problem_solving_data='{
    "systemMessage": "You are a problem-solving expert",
    "userMessages": ["I have a problem: my application is running slowly. How can I diagnose and fix this?"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$problem_solving_data" "Problem Solving Test"

# Agent Registry Tests
echo -e "${YELLOW}=== Agent Registry Tests ===${NC}"
test_endpoint "POST" "/api/agents/refresh" "" "Refresh Agent Registry"

# Test finding agents for specific requests
find_agents_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["I need help with coding"],
    "userId": "'$USER_ID'"
}'
test_endpoint "POST" "/api/agents/find" "$find_agents_data" "Find Agents for Coding Request"

# Context and Memory Tests
echo -e "${YELLOW}=== Context and Memory Tests ===${NC}"

# Get context after multiple interactions
test_endpoint "GET" "/conversation/$SESSION_ID/context?userId=$USER_ID&limit=10" "" "Get Session Context"

# Search memories
test_endpoint "GET" "/conversation/$SESSION_ID/memories?query=AI&userId=$USER_ID&limit=5" "" "Search Memories for 'AI'"

# Performance Tests
echo -e "${YELLOW}=== Performance Tests ===${NC}"

# Test multiple rapid requests
echo "Testing multiple rapid requests..."
for i in {1..3}; do
    rapid_data='{
        "systemMessage": "You are a helpful AI assistant",
        "userMessages": ["Quick test message number '$i'"],
        "userId": "'$USER_ID'",
        "sessionId": "'$SESSION_ID'"
    }'
    
    echo -e "${CYAN}Request $i:${NC}"
    test_endpoint "POST" "/conversation/message" "$rapid_data" "Rapid Request $i"
done

echo -e "${GREEN}🎉 Agent Testing Complete!${NC}"
echo ""
echo "Summary:"
echo "- Agent discovery: ✅"
echo "- Agent selection: ✅"
echo "- Specific agent tests: ✅"
echo "- Capability tests: ✅"
echo "- Registry tests: ✅"
echo "- Context/memory tests: ✅"
echo "- Performance tests: ✅"
echo ""
echo "You can view the results in the web interface at: $BASE_URL"
echo "Or use the view-contexts.sh script to examine stored data."
