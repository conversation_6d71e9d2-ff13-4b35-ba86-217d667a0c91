# Test-specific configuration
server:
  port: 0  # Use random available port for tests

# MCP Server Configuration for Tests
mcp:
  server:
    host: localhost
    port: 0  # Use random available port for tests
    max-connections: 10
    read-timeout: 5000
    write-timeout: 5000
    enable-ssl: false
  
  # Model Provider Configurations (empty for tests)
  models:
    openai:
      api-key: ""
      base-url: ""
    anthropic:
      api-key: ""
      base-url: ""
    local:
      base-url: ""

# Context Management Configuration
context:
  storage:
    max-contexts-per-user: 10
    max-context-size: 1000
    cleanup-interval: 60000  # 1 minute for tests
  memory:
    max-memories-per-user: 100
    memory-ttl: 60000  # 1 minute for tests

# Logging Configuration
logging:
  level:
    com.agentic.framework: DEBUG
    org.springframework.web: WARN
    org.springframework.security: WARN
