package com.agentic.framework;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Main application test class for the Agentic Framework
 * Tests that the Spring Boot application context loads successfully
 */
@SpringBootTest
@ActiveProfiles("test")
class AgenticFrameworkApplicationTests {

    @Test
    void contextLoads() {
        // This test will fail if the application context cannot start
    }
}
