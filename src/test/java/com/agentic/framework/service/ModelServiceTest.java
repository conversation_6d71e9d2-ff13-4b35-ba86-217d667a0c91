package com.agentic.framework.service;

import com.agentic.framework.mcp.ContextManager;
import com.agentic.framework.mcp.ModelRegistry;
import com.agentic.framework.mcp.McpServer;
import com.agentic.framework.mcp.ModelProvider;
import com.agentic.framework.mcp.ModelInfo;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ModelConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ModelService class
 */
@ExtendWith(MockitoExtension.class)
class ModelServiceTest {

    @Mock
    private ModelRegistry modelRegistry;
    
    @Mock
    private ContextManager contextManager;
    
    @Mock
    private McpServer mcpServer;
    
    @Mock
    private ModelProvider modelProvider;
    
    private ModelService modelService;
    
    @BeforeEach
    void setUp() {
        modelService = new ModelService(modelRegistry, contextManager, mcpServer);
    }
    
    @Test
    void testProcessRequest_Success() {
        // Arrange
        ModelRequest request = createValidRequest();
        ModelResponse expectedResponse = new ModelResponse("gpt-4", "Test response");
        expectedResponse.setStatus(ModelResponse.ResponseStatus.SUCCESS);
        
        when(modelRegistry.hasModel("gpt-4")).thenReturn(true);
        when(modelRegistry.getModelProvider("gpt-4")).thenReturn(modelProvider);
        when(modelProvider.getDefaultConfig()).thenReturn(new ModelConfig());
        when(modelProvider.processRequest(any(), any())).thenReturn(CompletableFuture.completedFuture(expectedResponse));
        
        // Act
        CompletableFuture<ModelResponse> future = modelService.processRequest(request);
        ModelResponse response = future.join();
        
        // Assert
        assertNotNull(response);
        assertEquals("gpt-4", response.getModelName());
        assertEquals("Test response", response.getResponse());
        assertEquals(ModelResponse.ResponseStatus.SUCCESS, response.getStatus());
        assertNotNull(response.getResponseId());
        assertEquals("user123", response.getUserId());
        assertEquals("session456", response.getSessionId());
        
        verify(contextManager).addContext("user123", "session456", request, response);
    }
    
    @Test
    void testProcessRequest_InvalidModel() {
        // Arrange
        ModelRequest request = createValidRequest();
        when(modelRegistry.hasModel("gpt-4")).thenReturn(false);
        
        // Act & Assert
        CompletableFuture<ModelResponse> future = modelService.processRequest(request);
        ModelResponse response = future.join();
        
        assertEquals(ModelResponse.ResponseStatus.ERROR, response.getStatus());
        assertTrue(response.getResponse().contains("Model not found"));
    }
    
    @Test
    void testProcessRequest_MissingSystemMessage() {
        // Arrange
        ModelRequest request = createValidRequest();
        request.setSystemMessage("");
        
        // Act & Assert
        CompletableFuture<ModelResponse> future = modelService.processRequest(request);
        ModelResponse response = future.join();
        
        assertEquals(ModelResponse.ResponseStatus.ERROR, response.getStatus());
        assertTrue(response.getResponse().contains("System message is required"));
    }
    
    @Test
    void testGetAvailableModels() {
        // Arrange
        when(modelRegistry.getAllModels()).thenReturn(Map.of("gpt-4", modelProvider));
        when(modelProvider.getModelName()).thenReturn("gpt-4");
        when(modelProvider.isAvailable()).thenReturn(true);
        when(modelProvider.isHealthy()).thenReturn(true);
        when(modelProvider.getModelInfo()).thenReturn(new ModelInfo("OpenAI", "GPT-4", "text-generation", true));
        
        // Act
        Map<String, Object> models = modelService.getAvailableModels();
        
        // Assert
        assertNotNull(models);
        assertEquals(1, models.size());
        assertTrue(models.containsKey("gpt-4"));
        
        @SuppressWarnings("unchecked")
        Map<String, Object> modelInfo = (Map<String, Object>) models.get("gpt-4");
        assertEquals("gpt-4", modelInfo.get("name"));
        assertEquals(true, modelInfo.get("available"));
        assertEquals(true, modelInfo.get("healthy"));
    }
    
    @Test
    void testGetModelInfo_Success() {
        // Arrange
        ModelInfo expectedInfo = new ModelInfo("OpenAI", "GPT-4", "text-generation", true);
        when(modelRegistry.hasModel("gpt-4")).thenReturn(true);
        when(modelRegistry.getModelProvider("gpt-4")).thenReturn(modelProvider);
        when(modelProvider.getModelInfo()).thenReturn(expectedInfo);
        
        // Act
        ModelInfo info = modelService.getModelInfo("gpt-4");
        
        // Assert
        assertNotNull(info);
        assertEquals(expectedInfo, info);
    }
    
    @Test
    void testGetModelInfo_NotFound() {
        // Arrange
        when(modelRegistry.hasModel("gpt-4")).thenReturn(false);
        
        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> modelService.getModelInfo("gpt-4"));
    }
    
    @Test
    void testGetUserContext() {
        // Arrange
        List<Object> expectedContext = List.of("context1", "context2");
//        when(contextManager.getContext("user123", "session456", 10)).thenReturn(expectedContext);

        // Act
        List<Object> context = modelService.getUserContext("user123", "session456", 10);
        
        // Assert
        assertEquals(expectedContext, context);
    }
    
    @Test
    void testClearUserSession() {
        // Act
        modelService.clearUserSession("user123", "session456");
        
        // Assert
        verify(contextManager).clearContext("user123", "session456");
    }
    
    @Test
    void testClearUserData() {
        // Act
        modelService.clearUserData("user123");
        
        // Assert
        verify(contextManager).clearUserData("user123");
    }
    
    @Test
    void testSearchMemories() {
        // Arrange
        List<Object> expectedMemories = List.of("memory1", "memory2");
//        when(contextManager.getMemories("user123", "test query", 10)).thenReturn(expectedMemories);
        
        // Act
        List<Object> memories = modelService.searchMemories("user123", "test query", 10);
        
        // Assert
        assertEquals(expectedMemories, memories);
    }
    
    @Test
    void testGetActiveConnections() {
        // Arrange
        when(mcpServer.getActiveConnections()).thenReturn(5);
        
        // Act
        int connections = modelService.getActiveConnections();
        
        // Assert
        assertEquals(5, connections);
    }
    
    private ModelRequest createValidRequest() {
        ModelRequest request = new ModelRequest();
        request.setSystemMessage("You are a helpful assistant");
        request.setUserMessages(List.of("Hello, how are you?"));
        request.setModelName("gpt-4");
        request.setUserId("user123");
        request.setSessionId("session456");
        return request;
    }
}
