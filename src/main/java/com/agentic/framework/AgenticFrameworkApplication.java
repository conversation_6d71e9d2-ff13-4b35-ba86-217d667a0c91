package com.agentic.framework;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Main Spring Boot application class for the Agentic Framework
 * Provides a unified interface for AI model interactions and context management
 */
@SpringBootApplication
@EnableAspectJAutoProxy
public class AgenticFrameworkApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(AgenticFrameworkApplication.class, args);
    }
}
