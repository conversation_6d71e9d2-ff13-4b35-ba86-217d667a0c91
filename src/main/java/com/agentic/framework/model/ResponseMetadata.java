package com.agentic.framework.model;

import java.time.LocalDateTime;

/**
 * Metadata class for model responses
 * Contains information about the response generation process
 */
public class ResponseMetadata {
    
    private String model;
    private String provider;
    private String requestId;
    private LocalDateTime generatedAt;
    private Long processingTimeMs;
    private String version;
    private String environment;
    
    public ResponseMetadata() {
        this.generatedAt = LocalDateTime.now();
    }
    
    public ResponseMetadata(String model, String provider) {
        this();
        this.model = model;
        this.provider = provider;
    }
    
    // Getters and Setters
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getProvider() {
        return provider;
    }
    
    public void setProvider(String provider) {
        this.provider = provider;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public LocalDateTime getGeneratedAt() {
        return generatedAt;
    }
    
    public void setGeneratedAt(LocalDateTime generatedAt) {
        this.generatedAt = generatedAt;
    }
    
    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }
    
    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getEnvironment() {
        return environment;
    }
    
    public void setEnvironment(String environment) {
        this.environment = environment;
    }
    
    @Override
    public String toString() {
        return "ResponseMetadata{" +
                "model='" + model + '\'' +
                ", provider='" + provider + '\'' +
                ", requestId='" + requestId + '\'' +
                ", generatedAt=" + generatedAt +
                ", processingTimeMs=" + processingTimeMs +
                ", version='" + version + '\'' +
                ", environment='" + environment + '\'' +
                '}';
    }
}
