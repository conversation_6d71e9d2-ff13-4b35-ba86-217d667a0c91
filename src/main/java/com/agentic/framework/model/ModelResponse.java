package com.agentic.framework.model;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response class for model interactions
 * Contains the generated response, metadata, and context information
 */
public class ModelResponse {
    
    private String responseId;
    private String modelName;
    private String response;
    private List<String> choices;
    private Integer tokensUsed;
    private Double cost;
    private LocalDateTime timestamp;
    private String userId;
    private String sessionId;
    private ResponseMetadata metadata;
    private ResponseStatus status;
    
    public enum ResponseStatus {
        SUCCESS, ERROR, PARTIAL, TIMEOUT
    }
    
    // Default constructor
    public ModelResponse() {
        this.timestamp = LocalDateTime.now();
        this.status = ResponseStatus.SUCCESS;
    }
    
    // Constructor with essential fields
    public ModelResponse(String modelName, String response) {
        this();
        this.modelName = modelName;
        this.response = response;
    }
    
    // Getters and Setters
    public String getResponseId() {
        return responseId;
    }
    
    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    
    public String getModelName() {
        return modelName;
    }
    
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    
    public String getResponse() {
        return response;
    }
    
    public void setResponse(String response) {
        this.response = response;
    }
    
    public List<String> getChoices() {
        return choices;
    }
    
    public void setChoices(List<String> choices) {
        this.choices = choices;
    }
    
    public Integer getTokensUsed() {
        return tokensUsed;
    }
    
    public void setTokensUsed(Integer tokensUsed) {
        this.tokensUsed = tokensUsed;
    }
    
    public Double getCost() {
        return cost;
    }
    
    public void setCost(Double cost) {
        this.cost = cost;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public ResponseMetadata getMetadata() {
        return metadata;
    }
    
    public void setMetadata(ResponseMetadata metadata) {
        this.metadata = metadata;
    }
    
    public ResponseStatus getStatus() {
        return status;
    }
    
    public void setStatus(ResponseStatus status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "ModelResponse{" +
                "responseId='" + responseId + '\'' +
                ", modelName='" + modelName + '\'' +
                ", response='" + response + '\'' +
                ", choices=" + choices +
                ", tokensUsed=" + tokensUsed +
                ", cost=" + cost +
                ", timestamp=" + timestamp +
                ", userId='" + userId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", metadata=" + metadata +
                ", status=" + status +
                '}';
    }
}
