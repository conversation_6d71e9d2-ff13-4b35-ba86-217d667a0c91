package com.agentic.framework.model;

/**
 * Configuration class for AI model parameters
 * Contains temperature, max tokens, and other model-specific settings
 */
public class ModelConfig {
    
    private Double temperature = 0.7;
    private Integer maxTokens = 4000;
    private Double topP = 1.0;
    private Integer topK = 40;
    private Double presencePenalty = 0.0;
    private Double frequencyPenalty = 0.0;
    private Boolean stream = false;
    private String stop;
    
    // Default constructor
    public ModelConfig() {}
    
    // Constructor with common parameters
    public ModelConfig(Double temperature, Integer maxTokens) {
        this.temperature = temperature;
        this.maxTokens = maxTokens;
    }
    
    // Getters and Setters
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Integer getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public Double getTopP() {
        return topP;
    }
    
    public void setTopP(Double topP) {
        this.topP = topP;
    }
    
    public Integer getTopK() {
        return topK;
    }
    
    public void setTopK(Integer topK) {
        this.topK = topK;
    }
    
    public Double getPresencePenalty() {
        return presencePenalty;
    }
    
    public void setPresencePenalty(Double presencePenalty) {
        this.presencePenalty = presencePenalty;
    }
    
    public Double getFrequencyPenalty() {
        return frequencyPenalty;
    }
    
    public void setFrequencyPenalty(Double frequencyPenalty) {
        this.frequencyPenalty = frequencyPenalty;
    }
    
    public Boolean getStream() {
        return stream;
    }
    
    public void setStream(Boolean stream) {
        this.stream = stream;
    }
    
    public String getStop() {
        return stop;
    }
    
    public void setStop(String stop) {
        this.stop = stop;
    }
    
    @Override
    public String toString() {
        return "ModelConfig{" +
                "temperature=" + temperature +
                ", maxTokens=" + maxTokens +
                ", topP=" + topP +
                ", topK=" + topK +
                ", presencePenalty=" + presencePenalty +
                ", frequencyPenalty=" + frequencyPenalty +
                ", stream=" + stream +
                ", stop='" + stop + '\'' +
                '}';
    }
}
