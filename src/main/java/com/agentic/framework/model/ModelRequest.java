package com.agentic.framework.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * DTO for model interaction requests
 * Contains system messages, user messages, and model configuration
 */
public class ModelRequest {
    
    @NotBlank(message = "System message is required")
    private String systemMessage;
    
    @NotNull(message = "User messages cannot be null")
    private List<String> userMessages;
    
    @NotBlank(message = "Model name is required")
    private String modelName;
    
    private String userId;
    private String sessionId;
    private ModelConfig config;
    
    // Default constructor
    public ModelRequest() {}
    
    // Constructor with required fields
    public ModelRequest(String systemMessage, List<String> userMessages, String modelName) {
        this.systemMessage = systemMessage;
        this.userMessages = userMessages;
        this.modelName = modelName;
    }
    
    // Getters and Setters
    public String getSystemMessage() {
        return systemMessage;
    }
    
    public void setSystemMessage(String systemMessage) {
        this.systemMessage = systemMessage;
    }
    
    public List<String> getUserMessages() {
        return userMessages;
    }
    
    public void setUserMessages(List<String> userMessages) {
        this.userMessages = userMessages;
    }
    
    public String getModelName() {
        return modelName;
    }
    
    public void setModelName(String modelName) {
        this.modelName = modelName;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public ModelConfig getConfig() {
        return config;
    }
    
    public void setConfig(ModelConfig config) {
        this.config = config;
    }
    
    @Override
    public String toString() {
        return "ModelRequest{" +
                "systemMessage='" + systemMessage + '\'' +
                ", userMessages=" + userMessages +
                ", modelName='" + modelName + '\'' +
                ", userId='" + userId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", config=" + config +
                '}';
    }
}
