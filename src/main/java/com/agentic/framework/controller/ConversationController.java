package com.agentic.framework.controller;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.service.ConversationService;
import com.agentic.framework.service.ToolExecutorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * REST Controller for Conversation Operations
 * Provides HTTP API endpoints for conversation management and MCP tool execution
 */
@RestController
@RequestMapping("/conversation")
@CrossOrigin(origins = "*")
public class ConversationController {
    
    private static final Logger logger = LoggerFactory.getLogger(ConversationController.class);
    
    @Autowired
    private ConversationService conversationService;
    
    @Autowired
    private ToolExecutorService toolExecutorService;
    
    /**
     * Send message to agent via MCP server
     * POST /conversation/message
     */
    @PostMapping("/message")
    public ResponseEntity<ConversationResponse> sendMessage(@Valid @RequestBody MessageRequest request) {
        try {
            // Generate session ID if not provided
            if (request.getSessionId() == null) {
                request.setSessionId(UUID.randomUUID().toString());
            }
            
            logger.info("Processing message for session: {}, user: {}", request.getSessionId(), request.getUserId());
            
            ModelResponse response = conversationService.processMessage(request);
            
            ConversationResponse conversationResponse = new ConversationResponse();
            conversationResponse.setSuccess(true);
            conversationResponse.setResponse(response);
            conversationResponse.setSessionId(request.getSessionId());
            conversationResponse.setTimestamp(System.currentTimeMillis());
            
            return ResponseEntity.ok(conversationResponse);
            
        } catch (Exception e) {
            logger.error("Error processing message", e);
            ConversationResponse errorResponse = new ConversationResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error processing message: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Retrieve context for a session
     * GET /conversation/{sessionId}/context
     */
    @GetMapping("/{sessionId}/context")
    public ResponseEntity<ContextResponse> getContext(
            @PathVariable String sessionId,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<Map<String, Object>> context = conversationService.getContext(sessionId, userId, limit);
            
            ContextResponse response = new ContextResponse();
            response.setSuccess(true);
            response.setContext(context);
            response.setSessionId(sessionId);
            response.setCount(context.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error retrieving context for session: {}", sessionId, e);
            ContextResponse errorResponse = new ContextResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error retrieving context: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Execute MCP tool
     * POST /conversation/tool/execute
     */
    @PostMapping("/tool/execute")
    public ResponseEntity<ToolResponse> executeTool(@Valid @RequestBody ToolRequest request) {
        try {
            logger.info("Executing tool: {} for session: {}", request.getToolName(), request.getSessionId());
            
            Map<String, Object> result = toolExecutorService.executeTool(request);
            
            ToolResponse response = new ToolResponse();
            response.setSuccess(true);
            response.setToolName(request.getToolName());
            response.setResult(result);
            response.setSessionId(request.getSessionId());
            response.setTimestamp(System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error executing tool: {}", request.getToolName(), e);
            ToolResponse errorResponse = new ToolResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error executing tool: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Get available tools from MCP server
     * GET /conversation/tools
     */
    @GetMapping("/tools")
    public ResponseEntity<ToolsResponse> getAvailableTools() {
        try {
            List<Map<String, Object>> tools = toolExecutorService.getAvailableTools();
            
            ToolsResponse response = new ToolsResponse();
            response.setSuccess(true);
            response.setTools(tools);
            response.setCount(tools.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error retrieving available tools", e);
            ToolsResponse errorResponse = new ToolsResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error retrieving tools: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Clear session context
     * DELETE /conversation/{sessionId}/context
     */
    @DeleteMapping("/{sessionId}/context")
    public ResponseEntity<BaseResponse> clearSessionContext(
            @PathVariable String sessionId,
            @RequestParam(required = false) String userId) {
        
        try {
            conversationService.clearSessionContext(sessionId, userId);
            
            BaseResponse response = new BaseResponse();
            response.setSuccess(true);
            response.setMessage("Session context cleared successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error clearing session context: {}", sessionId, e);
            BaseResponse errorResponse = new BaseResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error clearing session context: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * Search memories for a session
     * GET /conversation/{sessionId}/memories
     */
    @GetMapping("/{sessionId}/memories")
    public ResponseEntity<MemoriesResponse> searchMemories(
            @PathVariable String sessionId,
            @RequestParam String query,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<Map<String, Object>> memories = conversationService.searchMemories(sessionId, userId, query, limit);
            
            MemoriesResponse response = new MemoriesResponse();
            response.setSuccess(true);
            response.setMemories(memories);
            response.setSessionId(sessionId);
            response.setQuery(query);
            response.setCount(memories.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error searching memories for session: {}", sessionId, e);
            MemoriesResponse errorResponse = new MemoriesResponse();
            errorResponse.setSuccess(false);
            errorResponse.setError("Error searching memories: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    // Request and Response DTOs
    
    public static class MessageRequest {
        private String systemMessage;
        private List<String> userMessages;
        private String agentId;
        private String userId;
        private String sessionId;
        private Map<String, Object> parameters;
        
        // Getters and setters
        public String getSystemMessage() { return systemMessage; }
        public void setSystemMessage(String systemMessage) { this.systemMessage = systemMessage; }
        
        public List<String> getUserMessages() { return userMessages; }
        public void setUserMessages(List<String> userMessages) { this.userMessages = userMessages; }
        
        public String getAgentId() { return agentId; }
        public void setAgentId(String agentId) { this.agentId = agentId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }
    
    public static class ToolRequest {
        private String toolName;
        private Map<String, Object> parameters;
        private String sessionId;
        private String userId;
        
        // Getters and setters
        public String getToolName() { return toolName; }
        public void setToolName(String toolName) { this.toolName = toolName; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
    }
    
    public static class ConversationResponse extends BaseResponse {
        private ModelResponse response;
        private String sessionId;
        private long timestamp;
        
        // Getters and setters
        public ModelResponse getResponse() { return response; }
        public void setResponse(ModelResponse response) { this.response = response; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
    
    public static class ContextResponse extends BaseResponse {
        private List<Map<String, Object>> context;
        private String sessionId;
        private int count;
        
        // Getters and setters
        public List<Map<String, Object>> getContext() { return context; }
        public void setContext(List<Map<String, Object>> context) { this.context = context; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
    }
    
    public static class ToolResponse extends BaseResponse {
        private String toolName;
        private Map<String, Object> result;
        private String sessionId;
        private long timestamp;
        
        // Getters and setters
        public String getToolName() { return toolName; }
        public void setToolName(String toolName) { this.toolName = toolName; }
        
        public Map<String, Object> getResult() { return result; }
        public void setResult(Map<String, Object> result) { this.result = result; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
    
    public static class ToolsResponse extends BaseResponse {
        private List<Map<String, Object>> tools;
        private int count;
        
        // Getters and setters
        public List<Map<String, Object>> getTools() { return tools; }
        public void setTools(List<Map<String, Object>> tools) { this.tools = tools; }
        
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
    }
    
    public static class MemoriesResponse extends BaseResponse {
        private List<Map<String, Object>> memories;
        private String sessionId;
        private String query;
        private int count;
        
        // Getters and setters
        public List<Map<String, Object>> getMemories() { return memories; }
        public void setMemories(List<Map<String, Object>> memories) { this.memories = memories; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }
        
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
    }
    
    public static class BaseResponse {
        private boolean success;
        private String message;
        private String error;
        
        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
