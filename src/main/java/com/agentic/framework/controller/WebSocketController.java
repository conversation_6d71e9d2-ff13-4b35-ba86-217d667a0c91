package com.agentic.framework.controller;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.service.ConversationService;
import com.agentic.framework.service.ToolExecutorService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * WebSocket Controller for Real-time Communication
 * Provides WebSocket endpoints for live agent interactions and tool execution
 */
@Controller
public class WebSocketController {
    
    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);
    
    @Autowired
    private ConversationService conversationService;
    
    @Autowired
    private ToolExecutorService toolExecutorService;
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * Handle incoming WebSocket messages for agent communication
     */
    @MessageMapping("/agent/message")
    @SendTo("/topic/agent/response")
    public WebSocketResponse handleAgentMessage(@Payload WebSocketMessage message, 
                                               SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            logger.info("Received WebSocket message from session: {}", sessionId);
            
            // Convert WebSocket message to MessageRequest
            ConversationController.MessageRequest request = new ConversationController.MessageRequest();
            request.setSystemMessage(message.getSystemMessage());
            request.setUserMessages(message.getUserMessages());
            request.setAgentId(message.getAgentId());
            request.setUserId(message.getUserId());
            request.setSessionId(message.getSessionId() != null ? message.getSessionId() : UUID.randomUUID().toString());
            request.setParameters(message.getParameters());
            
            // Process the message
            ModelResponse response = conversationService.processMessage(request);
            
            // Create WebSocket response
            WebSocketResponse wsResponse = new WebSocketResponse();
            wsResponse.setType("agent_response");
            wsResponse.setSuccess(true);
            wsResponse.setData(Map.of(
                "response", response.getResponse(),
                "modelName", response.getModelName(),
                "sessionId", request.getSessionId(),
                "timestamp", System.currentTimeMillis()
            ));
            
            return wsResponse;
            
        } catch (Exception e) {
            logger.error("Error processing WebSocket agent message", e);
            
            WebSocketResponse errorResponse = new WebSocketResponse();
            errorResponse.setType("error");
            errorResponse.setSuccess(false);
            errorResponse.setError("Error processing message: " + e.getMessage());
            
            return errorResponse;
        }
    }
    
    /**
     * Handle WebSocket tool execution requests
     */
    @MessageMapping("/tool/execute")
    @SendTo("/topic/tool/response")
    public WebSocketResponse handleToolExecution(@Payload WebSocketToolRequest message,
                                                SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            logger.info("Received tool execution request from session: {}", sessionId);
            
            // Convert to ToolRequest
            ConversationController.ToolRequest request = new ConversationController.ToolRequest();
            request.setToolName(message.getToolName());
            request.setParameters(message.getParameters());
            request.setSessionId(message.getSessionId());
            request.setUserId(message.getUserId());
            
            // Execute the tool
            Map<String, Object> result = toolExecutorService.executeTool(request);
            
            // Create WebSocket response
            WebSocketResponse wsResponse = new WebSocketResponse();
            wsResponse.setType("tool_response");
            wsResponse.setSuccess(true);
            wsResponse.setData(Map.of(
                "toolName", message.getToolName(),
                "result", result,
                "sessionId", message.getSessionId(),
                "timestamp", System.currentTimeMillis()
            ));
            
            return wsResponse;
            
        } catch (Exception e) {
            logger.error("Error executing tool via WebSocket", e);
            
            WebSocketResponse errorResponse = new WebSocketResponse();
            errorResponse.setType("error");
            errorResponse.setSuccess(false);
            errorResponse.setError("Error executing tool: " + e.getMessage());
            
            return errorResponse;
        }
    }
    
    /**
     * Handle WebSocket context retrieval requests
     */
    @MessageMapping("/context/get")
    @SendTo("/topic/context/response")
    public WebSocketResponse handleContextRequest(@Payload WebSocketContextRequest message,
                                                 SimpMessageHeaderAccessor headerAccessor) {
        try {
            String sessionId = headerAccessor.getSessionId();
            logger.info("Received context request from session: {}", sessionId);
            
            // Get context
            var context = conversationService.getContext(
                message.getSessionId(), 
                message.getUserId(), 
                message.getLimit()
            );
            
            // Create WebSocket response
            WebSocketResponse wsResponse = new WebSocketResponse();
            wsResponse.setType("context_response");
            wsResponse.setSuccess(true);
            wsResponse.setData(Map.of(
                "context", context,
                "sessionId", message.getSessionId(),
                "count", context.size(),
                "timestamp", System.currentTimeMillis()
            ));
            
            return wsResponse;
            
        } catch (Exception e) {
            logger.error("Error retrieving context via WebSocket", e);
            
            WebSocketResponse errorResponse = new WebSocketResponse();
            errorResponse.setType("error");
            errorResponse.setSuccess(false);
            errorResponse.setError("Error retrieving context: " + e.getMessage());
            
            return errorResponse;
        }
    }
    
    /**
     * Send real-time updates to specific user
     */
    public void sendToUser(String userId, String destination, Object payload) {
        try {
            messagingTemplate.convertAndSendToUser(userId, destination, payload);
            logger.debug("Sent message to user: {} at destination: {}", userId, destination);
        } catch (Exception e) {
            logger.error("Error sending message to user: {}", userId, e);
        }
    }
    
    /**
     * Send real-time updates to specific session
     */
    public void sendToSession(String sessionId, String destination, Object payload) {
        try {
            messagingTemplate.convertAndSend("/topic/session/" + sessionId + destination, payload);
            logger.debug("Sent message to session: {} at destination: {}", sessionId, destination);
        } catch (Exception e) {
            logger.error("Error sending message to session: {}", sessionId, e);
        }
    }
    
    // WebSocket Message DTOs
    
    public static class WebSocketMessage {
        private String systemMessage;
        private java.util.List<String> userMessages;
        private String agentId;
        private String userId;
        private String sessionId;
        private Map<String, Object> parameters;
        
        // Getters and setters
        public String getSystemMessage() { return systemMessage; }
        public void setSystemMessage(String systemMessage) { this.systemMessage = systemMessage; }
        
        public java.util.List<String> getUserMessages() { return userMessages; }
        public void setUserMessages(java.util.List<String> userMessages) { this.userMessages = userMessages; }
        
        public String getAgentId() { return agentId; }
        public void setAgentId(String agentId) { this.agentId = agentId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }
    
    public static class WebSocketToolRequest {
        private String toolName;
        private Map<String, Object> parameters;
        private String sessionId;
        private String userId;
        
        // Getters and setters
        public String getToolName() { return toolName; }
        public void setToolName(String toolName) { this.toolName = toolName; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
        
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
    }
    
    public static class WebSocketContextRequest {
        private String sessionId;
        private String userId;
        private int limit;
        
        // Getters and setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public int getLimit() { return limit; }
        public void setLimit(int limit) { this.limit = limit; }
    }
    
    public static class WebSocketResponse {
        private String type;
        private boolean success;
        private Map<String, Object> data;
        private String error;
        
        // Getters and setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
