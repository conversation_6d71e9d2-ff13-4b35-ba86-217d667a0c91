package com.agentic.framework.controller;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.mcp.ModelInfo;
import com.agentic.framework.service.ModelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * REST controller for model interactions and management
 * Provides HTTP endpoints for the agentic framework
 */
@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
public class ModelController {
    
    private static final Logger logger = LoggerFactory.getLogger(ModelController.class);
    
    private final ModelService modelService;
    
    @Autowired
    public ModelController(ModelService modelService) {
        this.modelService = modelService;
    }
    
    /**
     * Process a model request asynchronously
     */
    @PostMapping("/models/process")
    public CompletableFuture<ResponseEntity<ModelResponse>> processRequest(
            @Valid @RequestBody ModelRequest request) {
        
        logger.info("Processing model request for model: {}, user: {}", 
                   request.getModelName(), request.getUserId());
        
        return modelService.processRequest(request)
            .thenApply(response -> {
                logger.info("Model request completed successfully for user: {}", request.getUserId());
                return ResponseEntity.ok(response);
            })
            .exceptionally(throwable -> {
                logger.error("Error processing model request", throwable);
                ModelResponse errorResponse = new ModelResponse(request.getModelName(), "Error: " + throwable.getMessage());
                errorResponse.setStatus(ModelResponse.ResponseStatus.ERROR);
                return ResponseEntity.internalServerError().body(errorResponse);
            });
    }
    
    /**
     * Get available models
     */
    @GetMapping("/models")
    public ResponseEntity<Map<String, Object>> getAvailableModels() {
        try {
            Map<String, Object> models = modelService.getAvailableModels();
            return ResponseEntity.ok(models);
        } catch (Exception e) {
            logger.error("Error getting available models", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about a specific model
     */
    @GetMapping("/models/{modelName}")
    public ResponseEntity<ModelInfo> getModelInfo(@PathVariable String modelName) {
        try {
            ModelInfo modelInfo = modelService.getModelInfo(modelName);
            return ResponseEntity.ok(modelInfo);
        } catch (IllegalArgumentException e) {
            logger.warn("Model not found: {}", modelName);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            logger.error("Error getting model info for: {}", modelName, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get user context
     */
    @GetMapping("/context/{userId}")
    public ResponseEntity<List<Object>> getUserContext(
            @PathVariable String userId,
            @RequestParam(required = false) String sessionId,
            @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<Object> context = modelService.getUserContext(userId, sessionId, limit);
            return ResponseEntity.ok(context);
        } catch (Exception e) {
            logger.error("Error getting context for user: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Clear user session
     */
    @DeleteMapping("/context/{userId}/session/{sessionId}")
    public ResponseEntity<Void> clearUserSession(
            @PathVariable String userId,
            @PathVariable String sessionId) {
        
        try {
            modelService.clearUserSession(userId, sessionId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error clearing session for user: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Clear all user data
     */
    @DeleteMapping("/context/{userId}")
    public ResponseEntity<Void> clearUserData(@PathVariable String userId) {
        try {
            modelService.clearUserData(userId);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("Error clearing data for user: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Search user memories
     */
    @GetMapping("/context/{userId}/memories")
    public ResponseEntity<List<Object>> searchMemories(
            @PathVariable String userId,
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int limit) {
        
        try {
            List<Object> memories = modelService.searchMemories(userId, query, limit);
            return ResponseEntity.ok(memories);
        } catch (Exception e) {
            logger.error("Error searching memories for user: {}", userId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get server health and status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealth() {
        try {
            Map<String, Object> health = Map.of(
                "status", "healthy",
                "activeConnections", modelService.getActiveConnections(),
                "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            logger.error("Error getting health status", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get server metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        try {
            Map<String, Object> metrics = Map.of(
                "activeConnections", modelService.getActiveConnections(),
                "availableModels", modelService.getAvailableModels().size(),
                "timestamp", System.currentTimeMillis()
            );
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            logger.error("Error getting metrics", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
