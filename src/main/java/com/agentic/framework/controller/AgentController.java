package com.agentic.framework.controller;

import com.agentic.framework.agent.Agent;
import com.agentic.framework.agent.AgentRegistry;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * REST Controller for Agent Operations
 * Provides HTTP API endpoints for agent interactions
 */
@RestController
@RequestMapping("/api/agents")
@CrossOrigin(origins = "*")
public class AgentController {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentController.class);
    
    @Autowired
    private AgentRegistry agentRegistry;
    
    /**
     * Get all available agents
     */
    @GetMapping
    public ResponseEntity<List<AgentRegistry.AgentInfo>> getAllAgents() {
        try {
            List<AgentRegistry.AgentInfo> agents = agentRegistry.getAgentInfo();
            logger.info("Retrieved {} agents", agents.size());
            return ResponseEntity.ok(agents);
        } catch (Exception e) {
            logger.error("Error retrieving agents", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get information about a specific agent
     */
    @GetMapping("/{agentId}")
    public ResponseEntity<AgentRegistry.AgentInfo> getAgent(@PathVariable String agentId) {
        try {
            AgentRegistry.AgentInfo agentInfo = agentRegistry.getAgentInfo(agentId);
            if (agentInfo != null) {
                logger.info("Retrieved agent info for: {}", agentId);
                return ResponseEntity.ok(agentInfo);
            } else {
                logger.warn("Agent not found: {}", agentId);
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error retrieving agent info for: {}", agentId, e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Process a request with automatic agent selection
     */
    @PostMapping("/process")
    public ResponseEntity<AgentResponse> processRequest(@RequestBody ModelRequest request) {
        try {
            // Generate session ID if not provided
            if (request.getSessionId() == null) {
                request.setSessionId(UUID.randomUUID().toString());
            }
            
            // Find the best agent for this request
            Agent bestAgent = agentRegistry.getBestAgentForRequest(request);
            
            if (bestAgent != null) {
                logger.info("Processing request with agent: {} ({})", bestAgent.getAgentName(), bestAgent.getAgentId());
                
                // Process the request
                CompletableFuture<ModelResponse> futureResponse = bestAgent.processRequest(request);
                ModelResponse modelResponse = futureResponse.get();
                
                // Build response with agent information
                AgentResponse response = new AgentResponse();
                response.setSuccess(true);
                response.setModelResponse(modelResponse);
                response.setSelectedAgentId(bestAgent.getAgentId());
                response.setSelectedAgentName(bestAgent.getAgentName());
                response.setAgentCapabilities(bestAgent.getCapabilities());
                
                return ResponseEntity.ok(response);
                
            } else {
                logger.warn("No suitable agent found for request");
                AgentResponse response = new AgentResponse();
                response.setSuccess(false);
                response.setError("No suitable agent found for this request");
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("Error processing request with agents", e);
            AgentResponse response = new AgentResponse();
            response.setSuccess(false);
            response.setError("Error processing request: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Process a request with a specific agent
     */
    @PostMapping("/{agentId}/process")
    public ResponseEntity<AgentResponse> processRequestWithAgent(
            @PathVariable String agentId, 
            @RequestBody ModelRequest request) {
        try {
            // Get the specific agent
            Agent agent = agentRegistry.getAgent(agentId);
            if (agent == null) {
                logger.warn("Agent not found: {}", agentId);
                AgentResponse response = new AgentResponse();
                response.setSuccess(false);
                response.setError("Agent not found: " + agentId);
                return ResponseEntity.notFound().build();
            }
            
            // Generate session ID if not provided
            if (request.getSessionId() == null) {
                request.setSessionId(UUID.randomUUID().toString());
            }
            
            logger.info("Processing request with specific agent: {} ({})", agent.getAgentName(), agentId);
            
            // Process the request
            CompletableFuture<ModelResponse> futureResponse = agent.processRequest(request);
            ModelResponse modelResponse = futureResponse.get();
            
            // Build response
            AgentResponse response = new AgentResponse();
            response.setSuccess(true);
            response.setModelResponse(modelResponse);
            response.setSelectedAgentId(agent.getAgentId());
            response.setSelectedAgentName(agent.getAgentName());
            response.setAgentCapabilities(agent.getCapabilities());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error processing request with agent: {}", agentId, e);
            AgentResponse response = new AgentResponse();
            response.setSuccess(false);
            response.setError("Error processing request: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Get agents that can handle a specific request
     */
    @PostMapping("/find")
    public ResponseEntity<List<AgentRegistry.AgentInfo>> findAgentsForRequest(@RequestBody ModelRequest request) {
        try {
            List<Agent> agents = agentRegistry.findAgentsForRequest(request);
            List<AgentRegistry.AgentInfo> agentInfo = agents.stream()
                    .map(agent -> agentRegistry.getAgentInfo(agent.getAgentId()))
                    .toList();
            
            logger.info("Found {} agents for request", agentInfo.size());
            return ResponseEntity.ok(agentInfo);
            
        } catch (Exception e) {
            logger.error("Error finding agents for request", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get agent count
     */
    @GetMapping("/count")
    public ResponseEntity<AgentCountResponse> getAgentCount() {
        try {
            int count = agentRegistry.getAgentCount();
            AgentCountResponse response = new AgentCountResponse();
            response.setCount(count);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error getting agent count", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Refresh the agent registry
     */
    @PostMapping("/refresh")
    public ResponseEntity<AgentResponse> refreshAgents() {
        try {
            agentRegistry.refresh();
            logger.info("Agent registry refreshed successfully");
            
            AgentResponse response = new AgentResponse();
            response.setSuccess(true);
            response.setMessage("Agent registry refreshed successfully");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error refreshing agent registry", e);
            AgentResponse response = new AgentResponse();
            response.setSuccess(false);
            response.setError("Error refreshing agent registry: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * Response wrapper for agent operations
     */
    public static class AgentResponse {
        private boolean success;
        private ModelResponse modelResponse;
        private String selectedAgentId;
        private String selectedAgentName;
        private java.util.Map<String, String> agentCapabilities;
        private String message;
        private String error;
        
        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public ModelResponse getModelResponse() { return modelResponse; }
        public void setModelResponse(ModelResponse modelResponse) { this.modelResponse = modelResponse; }
        
        public String getSelectedAgentId() { return selectedAgentId; }
        public void setSelectedAgentId(String selectedAgentId) { this.selectedAgentId = selectedAgentId; }
        
        public String getSelectedAgentName() { return selectedAgentName; }
        public void setSelectedAgentName(String selectedAgentName) { this.selectedAgentName = selectedAgentName; }
        
        public java.util.Map<String, String> getAgentCapabilities() { return agentCapabilities; }
        public void setAgentCapabilities(java.util.Map<String, String> agentCapabilities) { this.agentCapabilities = agentCapabilities; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
    
    /**
     * Response for agent count
     */
    public static class AgentCountResponse {
        private int count;
        
        public int getCount() { return count; }
        public void setCount(int count) { this.count = count; }
    }
}
