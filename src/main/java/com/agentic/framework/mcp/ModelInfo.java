package com.agentic.framework.mcp;

/**
 * Information class for AI models
 * Contains model capabilities, metadata, and configuration details
 */
public class ModelInfo {
    
    private String provider;
    private String description;
    private String type;
    private boolean available;
    private String version;
    private String contextWindow;
    private String maxTokens;
    private String[] supportedFeatures;
    
    public ModelInfo() {}
    
    public ModelInfo(String provider, String description, String type, boolean available) {
        this.provider = provider;
        this.description = description;
        this.type = type;
        this.available = available;
    }
    
    // Getters and Setters
    public String getProvider() {
        return provider;
    }
    
    public void setProvider(String provider) {
        this.provider = provider;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public boolean isAvailable() {
        return available;
    }
    
    public void setAvailable(boolean available) {
        this.available = available;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public String getContextWindow() {
        return contextWindow;
    }
    
    public void setContextWindow(String contextWindow) {
        this.contextWindow = contextWindow;
    }
    
    public String getMaxTokens() {
        return maxTokens;
    }
    
    public void setMaxTokens(String maxTokens) {
        this.maxTokens = maxTokens;
    }
    
    public String[] getSupportedFeatures() {
        return supportedFeatures;
    }
    
    public void setSupportedFeatures(String[] supportedFeatures) {
        this.supportedFeatures = supportedFeatures;
    }
    
    @Override
    public String toString() {
        return "ModelInfo{" +
                "provider='" + provider + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", available=" + available +
                ", version='" + version + '\'' +
                ", contextWindow='" + contextWindow + '\'' +
                ", maxTokens='" + maxTokens + '\'' +
                ", supportedFeatures=" + java.util.Arrays.toString(supportedFeatures) +
                '}';
    }
}
