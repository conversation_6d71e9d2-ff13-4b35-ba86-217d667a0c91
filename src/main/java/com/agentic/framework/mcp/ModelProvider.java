package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelConfig;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;

import java.util.concurrent.CompletableFuture;

/**
 * Interface for AI model providers
 * Defines the contract for different model implementations
 */
public interface ModelProvider {
    
    /**
     * Get the name of the model this provider handles
     * @return model name
     */
    String getModelName();
    
    /**
     * Check if the model is available and ready to use
     * @return true if model is available
     */
    boolean isAvailable();
    
    /**
     * Get the default configuration for this model
     * @return default model configuration
     */
    ModelConfig getDefaultConfig();
    
    /**
     * Process a model request asynchronously
     * @param request the model request
     * @param config model-specific configuration
     * @return CompletableFuture containing the model response
     */
    CompletableFuture<ModelResponse> processRequest(ModelRequest request, ModelConfig config);
    
    /**
     * Process a model request with default configuration
     * @param request the model request
     * @return CompletableFuture containing the model response
     */
    default CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return processRequest(request, getDefaultConfig());
    }
    
    /**
     * Get model information and capabilities
     * @return model information
     */
    ModelInfo getModelInfo();
    
    /**
     * Health check for the model
     * @return true if model is healthy
     */
    default boolean isHealthy() {
        return isAvailable();
    }
    
    /**
     * Get estimated cost for a request (if applicable)
     * @param request the model request
     * @return estimated cost in USD
     */
    default double estimateCost(ModelRequest request) {
        return 0.0; // Default implementation for free models
    }
}
