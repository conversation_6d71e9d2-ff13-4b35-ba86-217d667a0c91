package com.agentic.framework.mcp;

import com.agentic.framework.model.ModelConfig;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Local model provider implementation
 * Handles interactions with locally hosted models (e.g., Ollama, vLLM)
 */
public class LocalModelProvider implements ModelProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(LocalModelProvider.class);
    
    private final String baseUrl;
    private final RestTemplate restTemplate;
    private final ModelInfo modelInfo;
    
    public LocalModelProvider(String baseUrl) {
        this.baseUrl = baseUrl != null && !baseUrl.isEmpty() ? baseUrl : "http://localhost:11434";
        this.restTemplate = new RestTemplate();
        this.modelInfo = new ModelInfo("Local", "Local models", "text-generation", true);
    }
    
    @Override
    public String getModelName() {
        return "local";
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // Simple health check
            String healthUrl = baseUrl + "/api/tags";
            ResponseEntity<String> response = restTemplate.getForEntity(healthUrl, String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            logger.debug("Local model not available: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public ModelConfig getDefaultConfig() {
        ModelConfig config = new ModelConfig();
        config.setTemperature(0.7);
        config.setMaxTokens(4000);
        config.setTopP(1.0);
        config.setStream(false);
        return config;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request, ModelConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (!isAvailable()) {
                    throw new RuntimeException("Local model service not available");
                }
                
                // Prepare request payload for Ollama-compatible API
                Map<String, Object> payload = new HashMap<>();
                payload.put("model", request.getModelName());
                payload.put("prompt", buildPrompt(request));
                payload.put("stream", config.getStream());
                
                // Add optional parameters
                if (config.getTemperature() != null) {
                    payload.put("temperature", config.getTemperature());
                }
                if (config.getTopP() != null) {
                    payload.put("top_p", config.getTopP());
                }
                if (config.getTopK() != null) {
                    payload.put("top_k", config.getTopK());
                }
                
                // Make API call
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);
                String url = baseUrl + "/api/generate";
                
                ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
                
                if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                    return parseLocalResponse(response.getBody(), request);
                } else {
                    throw new RuntimeException("Local model API call failed: " + response.getStatusCode());
                }
                
            } catch (Exception e) {
                logger.error("Error processing local model request", e);
                ModelResponse errorResponse = new ModelResponse(request.getModelName(), "Error: " + e.getMessage());
                errorResponse.setStatus(ModelResponse.ResponseStatus.ERROR);
                return errorResponse;
            }
        });
    }
    
    @Override
    public ModelInfo getModelInfo() {
        return modelInfo;
    }
    
    private String buildPrompt(ModelRequest request) {
        StringBuilder prompt = new StringBuilder();
        
        // Add system message
        if (request.getSystemMessage() != null && !request.getSystemMessage().trim().isEmpty()) {
            prompt.append("System: ").append(request.getSystemMessage()).append("\n\n");
        }
        
        // Add user messages
        for (String userMsg : request.getUserMessages()) {
            prompt.append("User: ").append(userMsg).append("\n\n");
        }
        prompt.append("Assistant:");
        
        return prompt.toString();
    }
    
    private ModelResponse parseLocalResponse(Map responseBody, ModelRequest request) {
        ModelResponse response = new ModelResponse(request.getModelName(), "");
        
        try {
            String generatedText = (String) responseBody.get("response");
            if (generatedText != null) {
                response.setResponse(generatedText.trim());
            }
            
            // Parse usage information if available
            if (responseBody.containsKey("eval_count")) {
                Integer tokensUsed = (Integer) responseBody.get("eval_count");
                response.setTokensUsed(tokensUsed);
            }
            
            // Set metadata
            ResponseMetadata metadata = new ResponseMetadata();
            metadata.setModel(request.getModelName());
            metadata.setProvider("Local");
            metadata.setRequestId((String) responseBody.get("id"));
            response.setMetadata(metadata);
            
        } catch (Exception e) {
            logger.error("Error parsing local model response", e);
            response.setResponse("Error parsing response");
            response.setStatus(ModelResponse.ResponseStatus.ERROR);
        }
        
        return response;
    }
    
    @Override
    public double estimateCost(ModelRequest request) {
        // Local models are typically free
        return 0.0;
    }
}
