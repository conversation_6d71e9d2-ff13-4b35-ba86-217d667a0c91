package com.agentic.framework.mcp;

import com.agentic.framework.agent.Agent;
import com.agentic.framework.agent.AgentRegistry;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Simple MCP (Model Context Protocol) Server Implementation
 * Provides HTTP-based communication with proper MCP message format
 * Follows MCP specification for agent and model interactions
 * 
 * NOTE: Disabled to prevent port conflicts with McpServer
 */
// @Component - Disabled to prevent port conflicts
public class SimpleMcpServer {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleMcpServer.class);
    
    private final AgentRegistry agentRegistry;
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final Gson gson;
    
    private ServerSocket serverSocket;
    private ExecutorService executorService;
    private AtomicBoolean running = new AtomicBoolean(false);
    
    // MCP Protocol constants
    private static final String MCP_VERSION = "2024-11-05";
    private static final String SERVER_NAME = "Agentic Framework MCP Server";
    private static final String SERVER_VERSION = "1.0.0";
    
    @Autowired
    public SimpleMcpServer(AgentRegistry agentRegistry, ModelRegistry modelRegistry, 
                          ContextManager contextManager) {
        this.agentRegistry = agentRegistry;
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.gson = new Gson();
    }
    
    @PostConstruct
    public void startServer() {
        try {
            initializeServer();
            startServerThread();
            logger.info("Simple MCP Server started successfully on port {}", serverSocket.getLocalPort());
            logger.info("Available agents: {}", agentRegistry.getAgentCount());
            logger.info("Available models: {}", modelRegistry.getModelCount());
        } catch (IOException e) {
            logger.error("Failed to start Simple MCP Server", e);
            throw new RuntimeException("Failed to start Simple MCP Server", e);
        }
    }
    
    @PreDestroy
    public void stopServer() {
        shutdown();
    }
    
    private void initializeServer() throws IOException {
        serverSocket = new ServerSocket(8081); // Use different port to avoid conflicts
        executorService = Executors.newFixedThreadPool(10);
        running.set(true);
    }
    
    private void startServerThread() {
        new Thread(() -> {
            while (running.get() && !serverSocket.isClosed()) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    clientSocket.setSoTimeout(30000);
                    
                    if (running.get()) {
                        McpClientHandler clientHandler = new McpClientHandler(
                            clientSocket, agentRegistry, modelRegistry, contextManager, gson);
                        executorService.submit(clientHandler);
                        logger.debug("New MCP client connection accepted: {}", clientSocket.getInetAddress());
                    } else {
                        clientSocket.close();
                    }
                } catch (IOException e) {
                    if (running.get()) {
                        logger.error("Error accepting MCP client connection", e);
                    }
                }
            }
        }, "Simple-MCP-Server-Acceptor").start();
    }
    
    public void shutdown() {
        logger.info("Shutting down Simple MCP Server...");
        running.set(false);
        
        if (executorService != null) {
            executorService.shutdown();
        }
        
        if (serverSocket != null && !serverSocket.isClosed()) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                logger.error("Error closing server socket", e);
            }
        }
        
        logger.info("Simple MCP Server shutdown complete");
    }
    
    public boolean isRunning() {
        return running.get();
    }
    
    /**
     * MCP Client Handler for processing MCP protocol messages
     */
    private static class McpClientHandler implements Runnable {
        
        private final Socket clientSocket;
        private final AgentRegistry agentRegistry;
        private final ModelRegistry modelRegistry;
        private final ContextManager contextManager;
        private final Gson gson;
        private final String clientId;
        
        public McpClientHandler(Socket clientSocket, AgentRegistry agentRegistry, 
                              ModelRegistry modelRegistry, ContextManager contextManager, Gson gson) {
            this.clientSocket = clientSocket;
            this.agentRegistry = agentRegistry;
            this.modelRegistry = modelRegistry;
            this.contextManager = contextManager;
            this.gson = gson;
            this.clientId = UUID.randomUUID().toString();
        }
        
        @Override
        public void run() {
            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(clientSocket.getInputStream()));
                 java.io.PrintWriter writer = new java.io.PrintWriter(clientSocket.getOutputStream(), true)) {
                
                logger.info("MCP client handler started for client: {}", clientId);
                
                // Send initialization message
                sendInitializationMessage(writer);
                
                String inputLine;
                while ((inputLine = reader.readLine()) != null) {
                    try {
                        // Parse the MCP message
                        JsonObject mcpMessage = JsonParser.parseString(inputLine).getAsJsonObject();
                        String method = mcpMessage.get("method").getAsString();
                        String id = mcpMessage.has("id") ? mcpMessage.get("id").getAsString() : null;
                        
                        // Handle different MCP methods
                        JsonObject response = handleMcpMethod(method, mcpMessage, id);
                        
                        // Send response back to client
                        if (response != null) {
                            writer.println(response.toString());
                        }
                        
                    } catch (Exception e) {
                        logger.error("Error processing MCP message", e);
                        sendErrorResponse(writer, "error", "Failed to process message: " + e.getMessage(), null);
                    }
                }
                
            } catch (IOException e) {
                logger.error("Error in MCP client handler for client: {}", clientId, e);
            } finally {
                try {
                    clientSocket.close();
                    logger.info("MCP client connection closed for client: {}", clientId);
                } catch (IOException e) {
                    logger.error("Error closing client socket", e);
                }
            }
        }
        
        /**
         * Send initialization message to new client
         */
        private void sendInitializationMessage(java.io.PrintWriter writer) {
            JsonObject initMessage = new JsonObject();
            initMessage.addProperty("jsonrpc", "2.0");
            initMessage.addProperty("method", "initialize");
            
            JsonObject params = new JsonObject();
            params.addProperty("protocolVersion", MCP_VERSION);
            params.addProperty("capabilities", "agentic-framework");
            params.addProperty("serverInfo", SERVER_NAME);
            params.addProperty("serverVersion", SERVER_VERSION);
            
            // Add available agents
            JsonObject agentInfo = new JsonObject();
            agentRegistry.getAllAgents().forEach((id, agent) -> {
                JsonObject agentDetails = new JsonObject();
                agentDetails.addProperty("name", agent.getAgentName());
                agentDetails.addProperty("description", agent.getAgentDescription());
                agentDetails.addProperty("version", agent.getAgentVersion());
                agentDetails.addProperty("capabilities", gson.toJson(agent.getCapabilities()));
                agentInfo.add(id, agentDetails);
            });
            params.add("agents", agentInfo);
            
            // Add available models
            JsonObject modelInfo = new JsonObject();
            modelRegistry.getAllModels().forEach((name, provider) -> {
                JsonObject modelDetails = new JsonObject();
                modelDetails.addProperty("provider", provider.getClass().getSimpleName());
                modelInfo.add(name, modelDetails);
            });
            params.add("models", modelInfo);
            
            initMessage.add("params", params);
            
            writer.println(initMessage.toString());
        }
        
        /**
         * Handle different MCP methods
         */
        private JsonObject handleMcpMethod(String method, JsonObject message, String id) {
            switch (method) {
                case "tools/list":
                    return handleToolsList(id);
                case "tools/call":
                    return handleToolsCall(message, id);
                case "agents/list":
                    return handleAgentsList(id);
                case "agents/call":
                    return handleAgentsCall(message, id);
                case "models/list":
                    return handleModelsList(id);
                case "models/call":
                    return handleModelsCall(message, id);
                default:
                    return createErrorResponse(id, "method_not_found", "Unknown method: " + method);
            }
        }
        
        /**
         * Handle tools/list method
         */
        private JsonObject handleToolsList(String id) {
            JsonObject response = new JsonObject();
            response.addProperty("jsonrpc", "2.0");
            response.addProperty("id", id);
            
            JsonObject result = new JsonObject();
            JsonObject tools = new JsonObject();
            
            // Add agent tools
            agentRegistry.getAllAgents().forEach((agentId, agent) -> {
                JsonObject tool = new JsonObject();
                tool.addProperty("description", agent.getAgentDescription());
                tool.addProperty("inputSchema", "{\"type\": \"object\", \"properties\": {\"prompt\": {\"type\": \"string\"}}}");
                tools.add("agent:" + agentId, tool);
            });
            
            result.add("tools", tools);
            response.add("result", result);
            
            return response;
        }
        
        /**
         * Handle tools/call method
         */
        private JsonObject handleToolsCall(JsonObject message, String id) {
            try {
                JsonObject params = message.getAsJsonObject("params");
                String name = params.get("name").getAsString();
                JsonObject arguments = params.getAsJsonObject("arguments");
                
                if (name.startsWith("agent:")) {
                    String agentId = name.substring(6); // Remove "agent:" prefix
                    return handleAgentCall(agentId, arguments, id);
                } else {
                    return createErrorResponse(id, "invalid_tool", "Unknown tool: " + name);
                }
            } catch (Exception e) {
                return createErrorResponse(id, "tool_call_error", "Error calling tool: " + e.getMessage());
            }
        }
        
        /**
         * Handle agents/list method
         */
        private JsonObject handleAgentsList(String id) {
            JsonObject response = new JsonObject();
            response.addProperty("jsonrpc", "2.0");
            response.addProperty("id", id);
            
            JsonObject result = new JsonObject();
            JsonObject agents = new JsonObject();
            
            agentRegistry.getAllAgents().forEach((agentId, agent) -> {
                JsonObject agentInfo = new JsonObject();
                agentInfo.addProperty("name", agent.getAgentName());
                agentInfo.addProperty("description", agent.getAgentDescription());
                agentInfo.addProperty("version", agent.getAgentVersion());
                agentInfo.addProperty("capabilities", gson.toJson(agent.getCapabilities()));
                agents.add(agentId, agentInfo);
            });
            
            result.add("agents", agents);
            response.add("result", result);
            
            return response;
        }
        
        /**
         * Handle agents/call method
         */
        private JsonObject handleAgentsCall(JsonObject message, String id) {
            try {
                JsonObject params = message.getAsJsonObject("params");
                String agentId = params.get("name").getAsString();
                JsonObject arguments = params.getAsJsonObject("arguments");
                
                return handleAgentCall(agentId, arguments, id);
            } catch (Exception e) {
                return createErrorResponse(id, "agent_call_error", "Error calling agent: " + e.getMessage());
            }
        }
        
        /**
         * Handle agent call
         */
        private JsonObject handleAgentCall(String agentId, JsonObject arguments, String id) {
            try {
                Agent agent = agentRegistry.getAgent(agentId);
                if (agent == null) {
                    return createErrorResponse(id, "agent_not_found", "Agent not found: " + agentId);
                }
                
                // Create model request from arguments
                ModelRequest request = new ModelRequest();
                request.setSessionId(UUID.randomUUID().toString());
                request.setModelName("default");
                request.setSystemMessage("You are a helpful AI assistant.");
                request.setUserMessages(Arrays.asList(arguments.get("prompt").getAsString()));
                
                // Process request with agent
                CompletableFuture<ModelResponse> future = agent.processRequest(request);
                ModelResponse response = future.get(); // For simplicity, blocking call
                
                // Create MCP response
                JsonObject mcpResponse = new JsonObject();
                mcpResponse.addProperty("jsonrpc", "2.0");
                mcpResponse.addProperty("id", id);
                
                JsonObject result = new JsonObject();
                result.addProperty("content", response.getResponse());
                result.addProperty("agentId", agentId);
                result.addProperty("agentName", agent.getAgentName());
                
                mcpResponse.add("result", result);
                
                return mcpResponse;
                
            } catch (Exception e) {
                return createErrorResponse(id, "agent_execution_error", "Error executing agent: " + e.getMessage());
            }
        }
        
        /**
         * Handle models/list method
         */
        private JsonObject handleModelsList(String id) {
            JsonObject response = new JsonObject();
            response.addProperty("jsonrpc", "2.0");
            response.addProperty("id", id);
            
            JsonObject result = new JsonObject();
            JsonObject models = new JsonObject();
            
            modelRegistry.getAllModels().forEach((name, provider) -> {
                JsonObject modelInfo = new JsonObject();
                modelInfo.addProperty("provider", provider.getClass().getSimpleName());
                models.add(name, modelInfo);
            });
            
            result.add("models", models);
            response.add("result", result);
            
            return response;
        }
        
        /**
         * Handle models/call method
         */
        private JsonObject handleModelsCall(JsonObject message, String id) {
            try {
                JsonObject params = message.getAsJsonObject("params");
                String modelName = params.get("name").getAsString();
                JsonObject arguments = params.getAsJsonObject("arguments");
                
                ModelProvider provider = modelRegistry.getModelProvider(modelName);
                if (provider == null) {
                    return createErrorResponse(id, "model_not_found", "Model not found: " + modelName);
                }
                
                // Create model request
                ModelRequest request = new ModelRequest();
                request.setSessionId(UUID.randomUUID().toString());
                request.setModelName(modelName);
                request.setSystemMessage("You are a helpful AI assistant.");
                request.setUserMessages(Arrays.asList(arguments.get("prompt").getAsString()));
                
                // Process request
                CompletableFuture<ModelResponse> future = provider.processRequest(request);
                ModelResponse response = future.get(); // For simplicity, blocking call
                
                // Create MCP response
                JsonObject mcpResponse = new JsonObject();
                mcpResponse.addProperty("jsonrpc", "2.0");
                mcpResponse.addProperty("id", id);
                
                JsonObject result = new JsonObject();
                result.addProperty("content", response.getResponse());
                result.addProperty("modelName", modelName);
                
                mcpResponse.add("result", result);
                
                return mcpResponse;
                
            } catch (Exception e) {
                return createErrorResponse(id, "model_call_error", "Error calling model: " + e.getMessage());
            }
        }
        
        /**
         * Create error response
         */
        private JsonObject createErrorResponse(String id, String code, String message) {
            JsonObject response = new JsonObject();
            response.addProperty("jsonrpc", "2.0");
            response.addProperty("id", id);
            
            JsonObject error = new JsonObject();
            error.addProperty("code", code);
            error.addProperty("message", message);
            
            response.add("error", error);
            
            return response;
        }
        
        /**
         * Send error response to client
         */
        private void sendErrorResponse(java.io.PrintWriter writer, String code, String message, String id) {
            JsonObject errorResponse = createErrorResponse(id, code, message);
            writer.println(errorResponse.toString());
        }
    }
}
