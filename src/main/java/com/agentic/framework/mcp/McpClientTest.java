package com.agentic.framework.mcp;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.UUID;

/**
 * Simple MCP Client Test
 * Demonstrates how to connect to and interact with the MCP server
 * 
 * NOTE: Disabled to prevent dependency issues with SimpleMcpServer
 */
// @Component - Disabled to prevent dependency issues
public class McpClientTest implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(McpClientTest.class);
    private final Gson gson = new Gson();
    
    @Autowired
    private SimpleMcpServer mcpServer;
    
    @Override
    public void run(String... args) throws Exception {
        // Wait a bit for the server to start
        Thread.sleep(2000);
        
        if (mcpServer.isRunning()) {
            logger.info("MCP Server is running, starting client test...");
            testMcpClient();
        } else {
            logger.warn("MCP Server is not running, skipping client test");
        }
    }
    
    private void testMcpClient() {
        try (Socket socket = new Socket("localhost", 8081);
             BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
             PrintWriter writer = new PrintWriter(socket.getOutputStream(), true)) {
            
            logger.info("Connected to MCP server");
            
            // Wait for initialization message
            String initMessage = reader.readLine();
            logger.info("Received initialization: {}", initMessage);
            
            // Test 1: List available agents
            testListAgents(writer, reader);
            
            // Test 2: Call an agent
            testCallAgent(writer, reader);
            
            // Test 3: List available models
            testListModels(writer, reader);
            
            // Test 4: List available tools
            testListTools(writer, reader);
            
            logger.info("MCP client test completed successfully");
            
        } catch (Exception e) {
            logger.error("Error in MCP client test", e);
        }
    }
    
    private void testListAgents(PrintWriter writer, BufferedReader reader) throws Exception {
        logger.info("Testing agents/list...");
        
        JsonObject request = new JsonObject();
        request.addProperty("jsonrpc", "2.0");
        request.addProperty("method", "agents/list");
        request.addProperty("id", UUID.randomUUID().toString());
        
        writer.println(request.toString());
        
        String response = reader.readLine();
        logger.info("Agents list response: {}", response);
    }
    
    private void testCallAgent(PrintWriter writer, BufferedReader reader) throws Exception {
        logger.info("Testing agents/call...");
        
        JsonObject request = new JsonObject();
        request.addProperty("jsonrpc", "2.0");
        request.addProperty("method", "agents/call");
        request.addProperty("id", UUID.randomUUID().toString());
        
        JsonObject params = new JsonObject();
        params.addProperty("name", "time-series-forecasting-agent");
        
        JsonObject arguments = new JsonObject();
        arguments.addProperty("prompt", "Analyze this time series data: [1, 2, 3, 4, 5]");
        params.add("arguments", arguments);
        
        request.add("params", params);
        
        writer.println(request.toString());
        
        String response = reader.readLine();
        logger.info("Agent call response: {}", response);
    }
    
    private void testListModels(PrintWriter writer, BufferedReader reader) throws Exception {
        logger.info("Testing models/list...");
        
        JsonObject request = new JsonObject();
        request.addProperty("jsonrpc", "2.0");
        request.addProperty("method", "models/list");
        request.addProperty("id", UUID.randomUUID().toString());
        
        writer.println(request.toString());
        
        String response = reader.readLine();
        logger.info("Models list response: {}", response);
    }
    
    private void testListTools(PrintWriter writer, BufferedReader reader) throws Exception {
        logger.info("Testing tools/list...");
        
        JsonObject request = new JsonObject();
        request.addProperty("jsonrpc", "2.0");
        request.addProperty("method", "tools/list");
        request.addProperty("id", UUID.randomUUID().toString());
        
        writer.println(request.toString());
        
        String response = reader.readLine();
        logger.info("Tools list response: {}", response);
    }
}
