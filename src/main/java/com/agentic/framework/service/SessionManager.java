package com.agentic.framework.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing user sessions and conversation state
 * Handles session creation, timeout, and recovery
 */
@Service
public class SessionManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);
    
    private final ConcurrentHashMap<String, SessionInfo> activeSessions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor;
    
    @Value("${session.timeout:3600000}") // 1 hour default
    private long sessionTimeout;
    
    @Value("${session.max-sessions-per-user:10}")
    private int maxSessionsPerUser;
    
    public SessionManager() {
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        startCleanupTask();
    }
    
    /**
     * Create a new session for a user
     */
    public String createSession(String userId, String agentId) {
        String sessionId = UUID.randomUUID().toString();
        
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setSessionId(sessionId);
        sessionInfo.setUserId(userId);
        sessionInfo.setAgentId(agentId);
        sessionInfo.setCreatedAt(LocalDateTime.now());
        sessionInfo.setLastActivity(LocalDateTime.now());
        sessionInfo.setActive(true);
        
        // Check if user has too many active sessions
        cleanupUserSessions(userId);
        
        activeSessions.put(sessionId, sessionInfo);
        
        logger.info("Created new session: {} for user: {} with agent: {}", sessionId, userId, agentId);
        return sessionId;
    }
    
    /**
     * Get session information
     */
    public SessionInfo getSession(String sessionId) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null && session.isActive()) {
            session.setLastActivity(LocalDateTime.now());
            return session;
        }
        return null;
    }
    
    /**
     * Update session activity
     */
    public void updateSessionActivity(String sessionId) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null) {
            session.setLastActivity(LocalDateTime.now());
        }
    }
    
    /**
     * End a session
     */
    public void endSession(String sessionId) {
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null) {
            session.setActive(false);
            session.setEndedAt(LocalDateTime.now());
            logger.info("Ended session: {} for user: {}", sessionId, session.getUserId());
        }
    }
    
    /**
     * Get all active sessions for a user
     */
    public Map<String, SessionInfo> getUserSessions(String userId) {
        return activeSessions.entrySet().stream()
            .filter(entry -> entry.getValue().getUserId().equals(userId) && entry.getValue().isActive())
            .collect(ConcurrentHashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue()), ConcurrentHashMap::putAll);
    }
    
    /**
     * Recover session from MCP server
     */
    public SessionInfo recoverSession(String sessionId, String userId) {
        // In a real implementation, this would query the MCP server for session data
        SessionInfo session = activeSessions.get(sessionId);
        if (session != null && session.getUserId().equals(userId)) {
            session.setLastActivity(LocalDateTime.now());
            logger.info("Recovered session: {} for user: {}", sessionId, userId);
            return session;
        }
        return null;
    }
    
    /**
     * Get session statistics
     */
    public Map<String, Object> getSessionStats() {
        long activeCount = activeSessions.values().stream()
            .filter(SessionInfo::isActive)
            .count();
        
        long totalCount = activeSessions.size();
        
        return Map.of(
            "activeSessions", activeCount,
            "totalSessions", totalCount,
            "sessionTimeout", sessionTimeout,
            "maxSessionsPerUser", maxSessionsPerUser
        );
    }
    
    /**
     * Start the cleanup task for expired sessions
     */
    private void startCleanupTask() {
        cleanupExecutor.scheduleAtFixedRate(
            this::cleanupExpiredSessions,
            sessionTimeout,
            sessionTimeout,
            TimeUnit.MILLISECONDS
        );
        logger.info("Session cleanup task started with timeout: {} ms", sessionTimeout);
    }
    
    /**
     * Clean up expired sessions
     */
    private void cleanupExpiredSessions() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusSeconds(sessionTimeout / 1000);
            
            activeSessions.entrySet().removeIf(entry -> {
                SessionInfo session = entry.getValue();
                if (session.getLastActivity().isBefore(cutoffTime)) {
                    logger.info("Removing expired session: {} for user: {}", 
                        session.getSessionId(), session.getUserId());
                    return true;
                }
                return false;
            });
            
            logger.debug("Session cleanup completed. Active sessions: {}", activeSessions.size());
            
        } catch (Exception e) {
            logger.error("Error during session cleanup", e);
        }
    }
    
    /**
     * Clean up user sessions if they exceed the limit
     */
    private void cleanupUserSessions(String userId) {
        Map<String, SessionInfo> userSessions = getUserSessions(userId);
        
        if (userSessions.size() >= maxSessionsPerUser) {
            // Remove oldest sessions
            userSessions.entrySet().stream()
                .sorted((a, b) -> a.getValue().getLastActivity().compareTo(b.getValue().getLastActivity()))
                .limit(userSessions.size() - maxSessionsPerUser + 1)
                .forEach(entry -> {
                    endSession(entry.getKey());
                    logger.info("Removed old session: {} for user: {} due to session limit", 
                        entry.getKey(), userId);
                });
        }
    }
    
    /**
     * Shutdown the session manager
     */
    public void shutdown() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        logger.info("SessionManager shutdown complete");
    }
    
    /**
     * Session information class
     */
    public static class SessionInfo {
        private String sessionId;
        private String userId;
        private String agentId;
        private LocalDateTime createdAt;
        private LocalDateTime lastActivity;
        private LocalDateTime endedAt;
        private boolean active;
        private Map<String, Object> metadata;
        
        // Getters and setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getAgentId() { return agentId; }
        public void setAgentId(String agentId) { this.agentId = agentId; }
        
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
        
        public LocalDateTime getLastActivity() { return lastActivity; }
        public void setLastActivity(LocalDateTime lastActivity) { this.lastActivity = lastActivity; }
        
        public LocalDateTime getEndedAt() { return endedAt; }
        public void setEndedAt(LocalDateTime endedAt) { this.endedAt = endedAt; }
        
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }
}
