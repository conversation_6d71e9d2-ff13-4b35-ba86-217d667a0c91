package com.agentic.framework.service;

import com.agentic.framework.controller.ConversationController.ToolRequest;
import com.agentic.framework.mcp.ModelRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for executing MCP tools and managing tool discovery
 * Handles tool parameter validation and result processing
 */
@Service
public class ToolExecutorService {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolExecutorService.class);
    
    @Autowired
    private ModelRegistry modelRegistry;
    
    // Mock tool registry - in a real implementation, this would be populated from MCP server
    private final Map<String, ToolDefinition> availableTools = new HashMap<>();
    
    public ToolExecutorService() {
        // Initialize with some example tools
        initializeDefaultTools();
    }
    
    /**
     * Execute a tool with the given parameters
     */
    public Map<String, Object> executeTool(ToolRequest request) throws Exception {
        try {
            String toolName = request.getToolName();
            Map<String, Object> parameters = request.getParameters();
            
            logger.info("Executing tool: {} with parameters: {}", toolName, parameters);
            
            // Validate tool exists
            if (!availableTools.containsKey(toolName)) {
                throw new IllegalArgumentException("Tool not found: " + toolName);
            }
            
            ToolDefinition toolDef = availableTools.get(toolName);
            
            // Validate parameters
            validateToolParameters(toolDef, parameters);
            
            // Execute the tool
            Map<String, Object> result = executeToolImplementation(toolDef, parameters);
            
            logger.info("Tool execution completed: {}", toolName);
            return result;
            
        } catch (Exception e) {
            logger.error("Error executing tool: {}", request.getToolName(), e);
            throw e;
        }
    }
    
    /**
     * Get available tools from MCP server
     */
    public List<Map<String, Object>> getAvailableTools() {
        try {
            List<Map<String, Object>> toolsList = new ArrayList<>();
            
            for (Map.Entry<String, ToolDefinition> entry : availableTools.entrySet()) {
                ToolDefinition tool = entry.getValue();
                Map<String, Object> toolInfo = new HashMap<>();
                toolInfo.put("name", tool.getName());
                toolInfo.put("description", tool.getDescription());
                toolInfo.put("parameters", tool.getParameters());
                toolInfo.put("returnType", tool.getReturnType());
                
                toolsList.add(toolInfo);
            }
            
            return toolsList;
            
        } catch (Exception e) {
            logger.error("Error retrieving available tools", e);
            throw new RuntimeException("Error retrieving tools", e);
        }
    }
    
    /**
     * Validate tool parameters against the tool definition
     */
    private void validateToolParameters(ToolDefinition toolDef, Map<String, Object> parameters) {
        Map<String, String> requiredParams = toolDef.getParameters();
        
        for (Map.Entry<String, String> param : requiredParams.entrySet()) {
            String paramName = param.getKey();
            String paramType = param.getValue();
            
            if (!parameters.containsKey(paramName)) {
                throw new IllegalArgumentException("Missing required parameter: " + paramName);
            }
            
            Object value = parameters.get(paramName);
            if (!isValidParameterType(value, paramType)) {
                throw new IllegalArgumentException("Invalid parameter type for " + paramName + 
                    ". Expected " + paramType + ", got " + value.getClass().getSimpleName());
            }
        }
    }
    
    /**
     * Check if a parameter value matches the expected type
     */
    private boolean isValidParameterType(Object value, String expectedType) {
        if (value == null) return false;
        
        return switch (expectedType.toLowerCase()) {
            case "string" -> value instanceof String;
            case "number", "integer" -> value instanceof Number;
            case "boolean" -> value instanceof Boolean;
            case "object" -> value instanceof Map;
            case "array" -> value instanceof List;
            default -> true; // Unknown type, accept it
        };
    }
    
    /**
     * Execute the actual tool implementation
     */
    private Map<String, Object> executeToolImplementation(ToolDefinition toolDef, Map<String, Object> parameters) {
        // This is a mock implementation - in a real system, this would call the actual MCP tool
        Map<String, Object> result = new HashMap<>();
        
        switch (toolDef.getName()) {
            case "file_read":
                result.put("content", "Mock file content for: " + parameters.get("filepath"));
                result.put("size", 1024);
                break;
                
            case "web_search":
                result.put("results", List.of(
                    Map.of("title", "Search result 1", "url", "https://example.com/1"),
                    Map.of("title", "Search result 2", "url", "https://example.com/2")
                ));
                result.put("totalResults", 2);
                break;
                
            case "calculator":
                String expression = (String) parameters.get("expression");
                result.put("result", "Mock calculation result for: " + expression);
                result.put("expression", expression);
                break;
                
            case "weather":
                String location = (String) parameters.get("location");
                result.put("temperature", 22.5);
                result.put("condition", "Sunny");
                result.put("location", location);
                result.put("humidity", 65);
                break;
                
            default:
                result.put("message", "Tool executed successfully");
                result.put("parameters", parameters);
        }
        
        result.put("toolName", toolDef.getName());
        result.put("executionTime", System.currentTimeMillis());
        
        return result;
    }
    
    /**
     * Initialize default tools
     */
    private void initializeDefaultTools() {
        // File operations
        availableTools.put("file_read", new ToolDefinition(
            "file_read",
            "Read contents of a file",
            Map.of("filepath", "string"),
            "object"
        ));
        
        availableTools.put("file_write", new ToolDefinition(
            "file_write",
            "Write content to a file",
            Map.of("filepath", "string", "content", "string"),
            "object"
        ));
        
        // Web operations
        availableTools.put("web_search", new ToolDefinition(
            "web_search",
            "Search the web for information",
            Map.of("query", "string", "max_results", "number"),
            "object"
        ));
        
        availableTools.put("http_request", new ToolDefinition(
            "http_request",
            "Make an HTTP request",
            Map.of("url", "string", "method", "string", "headers", "object"),
            "object"
        ));
        
        // Utility tools
        availableTools.put("calculator", new ToolDefinition(
            "calculator",
            "Evaluate mathematical expressions",
            Map.of("expression", "string"),
            "object"
        ));
        
        availableTools.put("weather", new ToolDefinition(
            "weather",
            "Get weather information for a location",
            Map.of("location", "string"),
            "object"
        ));
        
        availableTools.put("time_converter", new ToolDefinition(
            "time_converter",
            "Convert time between timezones",
            Map.of("time", "string", "from_timezone", "string", "to_timezone", "string"),
            "object"
        ));
        
        logger.info("Initialized {} default tools", availableTools.size());
    }
    
    /**
     * Tool definition class
     */
    private static class ToolDefinition {
        private final String name;
        private final String description;
        private final Map<String, String> parameters;
        private final String returnType;
        
        public ToolDefinition(String name, String description, Map<String, String> parameters, String returnType) {
            this.name = name;
            this.description = description;
            this.parameters = parameters;
            this.returnType = returnType;
        }
        
        // Getters
        public String getName() { return name; }
        public String getDescription() { return description; }
        public Map<String, String> getParameters() { return parameters; }
        public String getReturnType() { return returnType; }
    }
}
