package com.agentic.framework.service;

import com.agentic.framework.mcp.*;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ModelConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Service layer for model interactions and context management
 * Orchestrates the business logic for the agentic framework
 */
@Service
public class ModelService {
    
    private static final Logger logger = LoggerFactory.getLogger(ModelService.class);
    
    private final ModelRegistry modelRegistry;
    private final ContextManager contextManager;
    private final McpServer mcpServer;
    
    @Autowired
    public ModelService(ModelRegistry modelRegistry, ContextManager contextManager, McpServer mcpServer) {
        this.modelRegistry = modelRegistry;
        this.contextManager = contextManager;
        this.mcpServer = mcpServer;
    }
    
    /**
     * Process a model request asynchronously
     */
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        Instant startTime = Instant.now();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Validate request
                validateRequest(request);
                
                // Get model provider
                var modelProvider = modelRegistry.getModelProvider(request.getModelName());
                
                // Merge configurations
                ModelConfig config = mergeConfigurations(request, modelProvider);
                
                // Process the request
                return modelProvider.processRequest(request, config);
                
            } catch (Exception e) {
                logger.error("Error processing model request: {}", e.getMessage());
                throw new RuntimeException("Failed to process model request: " + e.getMessage(), e);
            }
        }).thenCompose(future -> future)
        .thenApply(response -> {
            // Add metadata
            Duration processingTime = Duration.between(startTime, Instant.now());
            response.setResponseId(UUID.randomUUID().toString());
            response.setUserId(request.getUserId());
            response.setSessionId(request.getSessionId());
            
            // Add to context
            contextManager.addContext(
                request.getUserId(),
                request.getSessionId(),
                request,
                response
            );
            
            logger.info("Model request processed successfully for user: {}, model: {}, time: {}ms",
                       request.getUserId(), request.getModelName(), processingTime.toMillis());
            
            return response;
        });
    }
    
    /**
     * Get available models
     */
    public Map<String, Object> getAvailableModels() {
        try {
            var models = modelRegistry.getAllModels();
            return models.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> {
                        var provider = entry.getValue();
                        return Map.of(
                            "name", provider.getModelName(),
                            "available", provider.isAvailable(),
                            "healthy", provider.isHealthy(),
                            "info", provider.getModelInfo()
                        );
                    }
                ));
        } catch (Exception e) {
            logger.error("Error getting available models", e);
            throw new RuntimeException("Failed to get available models", e);
        }
    }
    
    /**
     * Get model information
     */
    public ModelInfo getModelInfo(String modelName) {
        try {
            if (!modelRegistry.hasModel(modelName)) {
                throw new IllegalArgumentException("Model not found: " + modelName);
            }
            
            var provider = modelRegistry.getModelProvider(modelName);
            return provider.getModelInfo();
            
        } catch (Exception e) {
            logger.error("Error getting model info for: {}", modelName, e);
            throw new RuntimeException("Failed to get model info: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get user context
     */
    public List<Object> getUserContext(String userId, String sessionId, int limit) {
        try {
            return Collections.singletonList(contextManager.getContext(userId, sessionId, limit));
        } catch (Exception e) {
            logger.error("Error getting context for user: {}", userId, e);
            throw new RuntimeException("Failed to get user context: " + e.getMessage(), e);
        }
    }
    
    /**
     * Clear user session
     */
    public void clearUserSession(String userId, String sessionId) {
        try {
            contextManager.clearContext(userId, sessionId);
            logger.info("Cleared session for user: {}, session: {}", userId, sessionId);
        } catch (Exception e) {
            logger.error("Error clearing session for user: {}", userId, e);
            throw new RuntimeException("Failed to clear user session: " + e.getMessage(), e);
        }
    }
    
    /**
     * Clear all user data
     */
    public void clearUserData(String userId) {
        try {
            contextManager.clearUserData(userId);
            logger.info("Cleared all data for user: {}", userId);
        } catch (Exception e) {
            logger.error("Error clearing data for user: {}", userId, e);
            throw new RuntimeException("Failed to clear user data: " + e.getMessage(), e);
        }
    }
    
    /**
     * Search user memories
     */
    public List<Object> searchMemories(String userId, String query, int limit) {
        try {
            return Collections.singletonList(contextManager.getMemories(userId, query, limit));
        } catch (Exception e) {
            logger.error("Error searching memories for user: {}", userId, e);
            throw new RuntimeException("Failed to search memories: " + e.getMessage(), e);
        }
    }
    
    /**
     * Get active connections count
     */
    public int getActiveConnections() {
        return mcpServer.getActiveConnections();
    }
    
    /**
     * Validate model request
     */
    private void validateRequest(ModelRequest request) {
        if (request.getSystemMessage() == null || request.getSystemMessage().trim().isEmpty()) {
            throw new IllegalArgumentException("System message is required");
        }
        
        if (request.getUserMessages() == null || request.getUserMessages().isEmpty()) {
            throw new IllegalArgumentException("User messages are required");
        }
        
        if (request.getModelName() == null || request.getModelName().trim().isEmpty()) {
            throw new IllegalArgumentException("Model name is required");
        }
        
        if (!modelRegistry.hasModel(request.getModelName())) {
            throw new IllegalArgumentException("Model not found: " + request.getModelName());
        }
    }
    
    /**
     * Merge request configuration with model default configuration
     */
    private ModelConfig mergeConfigurations(ModelRequest request, ModelProvider modelProvider) {
        ModelConfig defaultConfig = modelProvider.getDefaultConfig();
        ModelConfig requestConfig = request.getConfig();
        
        if (requestConfig == null) {
            return defaultConfig;
        }
        
        // Create merged config
        ModelConfig mergedConfig = new ModelConfig();
        
        // Use request config values if present, otherwise use defaults
        mergedConfig.setTemperature(requestConfig.getTemperature() != null ? 
            requestConfig.getTemperature() : defaultConfig.getTemperature());
        mergedConfig.setMaxTokens(requestConfig.getMaxTokens() != null ? 
            requestConfig.getMaxTokens() : defaultConfig.getMaxTokens());
        mergedConfig.setTopP(requestConfig.getTopP() != null ? 
            requestConfig.getTopP() : defaultConfig.getTopP());
        mergedConfig.setTopK(requestConfig.getTopK() != null ? 
            requestConfig.getTopK() : defaultConfig.getTopK());
        mergedConfig.setPresencePenalty(requestConfig.getPresencePenalty() != null ? 
            requestConfig.getPresencePenalty() : defaultConfig.getPresencePenalty());
        mergedConfig.setFrequencyPenalty(requestConfig.getFrequencyPenalty() != null ? 
            requestConfig.getFrequencyPenalty() : defaultConfig.getFrequencyPenalty());
        mergedConfig.setStream(requestConfig.getStream() != null ? 
            requestConfig.getStream() : defaultConfig.getStream());
        mergedConfig.setStop(requestConfig.getStop() != null ? 
            requestConfig.getStop() : defaultConfig.getStop());
        
        return mergedConfig;
    }
    
    // Helper method for creating maps
    private static Map<String, Object> Map(Object... keyValuePairs) {
        Map<String, Object> map = new java.util.HashMap<>();
        for (int i = 0; i < keyValuePairs.length; i += 2) {
            if (i + 1 < keyValuePairs.length) {
                map.put(keyValuePairs[i].toString(), keyValuePairs[i + 1]);
            }
        }
        return map;
    }
}
