package com.agentic.framework.service;

import com.agentic.framework.agent.Agent;
import com.agentic.framework.agent.AgentRegistry;
import com.agentic.framework.controller.ConversationController.MessageRequest;
import com.agentic.framework.mcp.ContextManager;
import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Service for managing conversations and context through MCP server
 * Handles message routing, context storage, and memory operations
 */
@Service
public class ConversationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ConversationService.class);
    
    @Autowired
    private AgentRegistry agentRegistry;
    
    @Autowired
    private ContextManager contextManager;
    
    /**
     * Process a message through the appropriate agent via MCP server
     */
    public ModelResponse processMessage(MessageRequest request) throws Exception {
        try {
            // Convert MessageRequest to ModelRequest
            ModelRequest modelRequest = new ModelRequest();
            modelRequest.setSystemMessage(request.getSystemMessage());
            modelRequest.setUserMessages(request.getUserMessages());
            modelRequest.setUserId(request.getUserId());
            modelRequest.setSessionId(request.getSessionId());
            
            // Determine which agent to use
            Agent agent;
            if (request.getAgentId() != null) {
                agent = agentRegistry.getAgent(request.getAgentId());
                if (agent == null) {
                    throw new IllegalArgumentException("Agent not found: " + request.getAgentId());
                }
            } else {
                // Auto-select best agent
                agent = agentRegistry.getBestAgentForRequest(modelRequest);
                if (agent == null) {
                    throw new IllegalArgumentException("No suitable agent found for this request");
                }
            }
            
            logger.info("Processing message with agent: {} ({})", agent.getAgentName(), agent.getAgentId());
            
            // Process the request
            CompletableFuture<ModelResponse> futureResponse = agent.processRequest(modelRequest);
            ModelResponse response = futureResponse.get();
            
            // Store context and memory
            contextManager.addContext(request.getUserId(), request.getSessionId(), modelRequest, response);
            
            return response;
            
        } catch (Exception e) {
            logger.error("Error processing message", e);
            throw e;
        }
    }
    
    /**
     * Get context for a session
     */
    public List<Map<String, Object>> getContext(String sessionId, String userId, int limit) {
        try {
            List<Map<String, Object>> contextList = new ArrayList<>();
            
            // Get context from ContextManager
            var interactions = contextManager.getContext(userId, sessionId, limit);
            
            for (var interaction : interactions) {
                Map<String, Object> contextEntry = new HashMap<>();
                contextEntry.put("timestamp", interaction.getTimestamp());
                contextEntry.put("request", Map.of(
                    "systemMessage", interaction.getRequest().getSystemMessage(),
                    "userMessages", interaction.getRequest().getUserMessages(),
                    "modelName", interaction.getRequest().getModelName()
                ));
                contextEntry.put("response", Map.of(
                    "response", interaction.getResponse().getResponse(),
                    "status", interaction.getResponse().getStatus().toString()
                ));
                
                contextList.add(contextEntry);
            }
            
            return contextList;
            
        } catch (Exception e) {
            logger.error("Error retrieving context for session: {}", sessionId, e);
            throw new RuntimeException("Error retrieving context", e);
        }
    }
    
    /**
     * Search memories for a session
     */
    public List<Map<String, Object>> searchMemories(String sessionId, String userId, String query, int limit) {
        try {
            List<Map<String, Object>> memoriesList = new ArrayList<>();
            
            // Get memories from ContextManager
            var memories = contextManager.getMemories(userId, query, limit);
            
            for (var memory : memories) {
                Map<String, Object> memoryEntry = new HashMap<>();
                memoryEntry.put("timestamp", memory.getTimestamp());
                memoryEntry.put("request", Map.of(
                    "systemMessage", memory.getRequest().getSystemMessage(),
                    "userMessages", memory.getRequest().getUserMessages(),
                    "modelName", memory.getRequest().getModelName()
                ));
                memoryEntry.put("response", Map.of(
                    "response", memory.getResponse().getResponse(),
                    "status", memory.getResponse().getStatus().toString()
                ));
                
                memoriesList.add(memoryEntry);
            }
            
            return memoriesList;
            
        } catch (Exception e) {
            logger.error("Error searching memories for session: {}", sessionId, e);
            throw new RuntimeException("Error searching memories", e);
        }
    }
    
    /**
     * Clear session context
     */
    public void clearSessionContext(String sessionId, String userId) {
        try {
            contextManager.clearContext(userId, sessionId);
            logger.info("Cleared context for session: {}, user: {}", sessionId, userId);
        } catch (Exception e) {
            logger.error("Error clearing session context: {}", sessionId, e);
            throw new RuntimeException("Error clearing session context", e);
        }
    }
    
    /**
     * Get available agents
     */
    public List<Map<String, Object>> getAvailableAgents() {
        try {
            List<Map<String, Object>> agentsList = new ArrayList<>();
            var agents = agentRegistry.getAgentInfo();
            
            for (var agent : agents) {
                Map<String, Object> agentInfo = new HashMap<>();
                agentInfo.put("agentId", agent.getAgentId());
                agentInfo.put("agentName", agent.getAgentName());
                agentInfo.put("description", agent.getDescription());
                agentInfo.put("capabilities", agent.getCapabilities());
                
                agentsList.add(agentInfo);
            }
            
            return agentsList;
            
        } catch (Exception e) {
            logger.error("Error retrieving available agents", e);
            throw new RuntimeException("Error retrieving agents", e);
        }
    }
}
