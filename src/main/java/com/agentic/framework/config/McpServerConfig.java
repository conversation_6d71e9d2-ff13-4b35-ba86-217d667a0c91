package com.agentic.framework.config;

import com.agentic.framework.mcp.McpServer;
import com.agentic.framework.mcp.ModelRegistry;
import com.agentic.framework.mcp.ContextManager;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for the MCP server
 * Maps to application.yml configuration values
 */
@Component
@ConfigurationProperties(prefix = "mcp.server")
public class McpServerConfig {
    
    private String host = "localhost";
    private int port = 8080;
    private int maxConnections = 100;
    private int readTimeout = 30000;
    private int writeTimeout = 30000;
    private boolean enableSsl = false;
    private String sslKeyStore;
    private String sslKeyStorePassword;
    
    // Default constructor
    public McpServerConfig() {}
    
    /**
     * Create McpServer bean for dependency injection
     */
    @Bean
    public McpServer mcpServer(ModelRegistry modelRegistry, ContextManager contextManager) {
        return new McpServer(this, modelRegistry, contextManager);
    }
    
    // Getters and Setters
    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public int getMaxConnections() {
        return maxConnections;
    }
    
    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public int getWriteTimeout() {
        return writeTimeout;
    }
    
    public void setWriteTimeout(int writeTimeout) {
        this.writeTimeout = writeTimeout;
    }
    
    public boolean isEnableSsl() {
        return enableSsl;
    }
    
    public void setEnableSsl(boolean enableSsl) {
        this.enableSsl = enableSsl;
    }
    
    public String getSslKeyStore() {
        return sslKeyStore;
    }
    
    public void setSslKeyStore(String sslKeyStore) {
        this.sslKeyStore = sslKeyStore;
    }
    
    public String getSslKeyStorePassword() {
        return sslKeyStorePassword;
    }
    
    public void setSslKeyStorePassword(String sslKeyStorePassword) {
        this.sslKeyStorePassword = sslKeyStorePassword;
    }
    
    @Override
    public String toString() {
        return "McpServerConfig{" +
                "host='" + host + '\'' +
                ", port=" + port +
                ", maxConnections=" + maxConnections +
                ", readTimeout=" + readTimeout +
                ", writeTimeout=" + writeTimeout +
                ", enableSsl=" + enableSsl +
                ", sslKeyStore='" + sslKeyStore + '\'' +
                ", sslKeyStorePassword='" + (sslKeyStorePassword != null ? "***" : "null") + '\'' +
                '}';
    }
}
