package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Registry for managing all available agents in the framework
 * Provides dynamic agent discovery, registration, and routing capabilities
 * Based on Google ADK patterns for agent management
 */
@Component
public class AgentRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(AgentRegistry.class);
    
    private final Map<String, Agent> agents = new ConcurrentHashMap<>();
    private final Map<String, AgentConfig> agentConfigs = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        logger.info("Initializing Agent Registry...");
        registerDefaultAgents();
        logger.info("Agent Registry initialized with {} agents", agents.size());
    }
    
    @PreDestroy
    public void cleanup() {
        logger.info("Cleaning up Agent Registry...");
        agents.values().forEach(Agent::cleanup);
        agents.clear();
        agentConfigs.clear();
        logger.info("Agent Registry cleanup complete");
    }
    
    /**
     * Register a new agent
     * @param agent the agent to register
     */
    public void registerAgent(Agent agent) {
        String agentId = agent.getAgentId();
        agents.put(agentId, agent);
        logger.info("Registered agent: {} ({})", agent.getAgentName(), agentId);
    }
    
    /**
     * Unregister an agent
     * @param agentId the ID of the agent to unregister
     */
    public void unregisterAgent(String agentId) {
        Agent agent = agents.remove(agentId);
        if (agent != null) {
            agent.cleanup();
            logger.info("Unregistered agent: {} ({})", agent.getAgentName(), agentId);
        }
    }
    
    /**
     * Get an agent by ID
     * @param agentId the agent ID
     * @return the agent, or null if not found
     */
    public Agent getAgent(String agentId) {
        return agents.get(agentId);
    }
    
    /**
     * Get all registered agents
     * @return map of agent ID to agent
     */
    public Map<String, Agent> getAllAgents() {
        return new HashMap<>(agents);
    }
    
    /**
     * Find agents that can handle a specific request
     * @param request the request to check
     * @return list of agents that can handle the request, sorted by priority
     */
    public List<Agent> findAgentsForRequest(ModelRequest request) {
        return agents.values().stream()
                .filter(agent -> agent.canHandle(request))
                .sorted((a1, a2) -> Integer.compare(a2.getPriority(), a1.getPriority()))
                .collect(Collectors.toList());
    }
    
    /**
     * Get the best agent for a request
     * @param request the request to process
     * @return the best agent, or null if no agent can handle the request
     */
    public Agent getBestAgentForRequest(ModelRequest request) {
        List<Agent> candidates = findAgentsForRequest(request);
        return candidates.isEmpty() ? null : candidates.get(0);
    }
    
    /**
     * Get agent information for all registered agents
     * @return list of agent information
     */
    public List<AgentInfo> getAgentInfo() {
        return agents.values().stream()
                .map(this::createAgentInfo)
                .collect(Collectors.toList());
    }
    
    /**
     * Get information about a specific agent
     * @param agentId the agent ID
     * @return agent information, or null if not found
     */
    public AgentInfo getAgentInfo(String agentId) {
        Agent agent = agents.get(agentId);
        return agent != null ? createAgentInfo(agent) : null;
    }
    
    /**
     * Set configuration for an agent
     * @param agentId the agent ID
     * @param config the agent configuration
     */
    public void setAgentConfig(String agentId, AgentConfig config) {
        agentConfigs.put(agentId, config);
        Agent agent = agents.get(agentId);
        if (agent != null) {
            agent.initialize(config.getParameters());
        }
        logger.debug("Updated config for agent: {}", agentId);
    }
    
    /**
     * Get configuration for an agent
     * @param agentId the agent ID
     * @return the agent configuration, or null if not found
     */
    public AgentConfig getAgentConfig(String agentId) {
        return agentConfigs.get(agentId);
    }
    
    /**
     * Check if an agent is registered
     * @param agentId the agent ID
     * @return true if the agent is registered
     */
    public boolean hasAgent(String agentId) {
        return agents.containsKey(agentId);
    }
    
    /**
     * Get the number of registered agents
     * @return number of agents
     */
    public int getAgentCount() {
        return agents.size();
    }
    
    /**
     * Refresh the agent registry
     * This will reinitialize all agents
     */
    public void refresh() {
        logger.info("Refreshing Agent Registry...");
        cleanup();
        initialize();
        logger.info("Agent Registry refresh complete");
    }
    
    private void registerDefaultAgents() {
        // Register default agents
        registerAgent(new GeneralPurposeAgent());
        registerAgent(new TimeSeriesForecastingAgent());
        registerAgent(new SoftwareBugAssistantAgent());
        registerAgent(new DataAnalysisAgent());
        registerAgent(new CodeReviewAgent());
    }
    
    private AgentInfo createAgentInfo(Agent agent) {
        return new AgentInfo(
                agent.getAgentId(),
                agent.getAgentName(),
                agent.getAgentDescription(),
                agent.getAgentVersion(),
                agent.getCapabilities(),
                agent.getPriority()
        );
    }
    
    /**
     * Information about an agent
     */
    public static class AgentInfo {
        private final String agentId;
        private final String name;
        private final String description;
        private final String version;
        private final Map<String, String> capabilities;
        private final int priority;
        
        public AgentInfo(String agentId, String name, String description, String version, 
                        Map<String, String> capabilities, int priority) {
            this.agentId = agentId;
            this.name = name;
            this.description = description;
            this.version = version;
            this.capabilities = capabilities;
            this.priority = priority;
        }
        
        // Getters
        public String getAgentId() { return agentId; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getVersion() { return version; }
        public Map<String, String> getCapabilities() { return capabilities; }
        public int getPriority() { return priority; }
    }
    
    /**
     * Configuration for an agent
     */
    public static class AgentConfig {
        private Map<String, Object> parameters = new HashMap<>();
        private boolean enabled = true;
        private int maxConcurrentRequests = 10;
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public int getMaxConcurrentRequests() { return maxConcurrentRequests; }
        public void setMaxConcurrentRequests(int maxConcurrentRequests) { this.maxConcurrentRequests = maxConcurrentRequests; }
    }
}
