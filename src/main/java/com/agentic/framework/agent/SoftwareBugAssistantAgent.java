package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import com.agentic.framework.model.ModelResponse.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * Software Bug Assistant Agent
 * Specialized agent for software debugging and bug analysis
 * Based on Google ADK software bug assistant patterns
 */
@Component
public class SoftwareBugAssistantAgent implements Agent {
    
    private static final Logger logger = LoggerFactory.getLogger(SoftwareBugAssistantAgent.class);
    
    private static final String AGENT_ID = "software-bug-assistant-agent";
    private static final String AGENT_NAME = "Software Bug Assistant Agent";
    private static final String AGENT_DESCRIPTION = "Specialized agent for software debugging, bug analysis, and code problem resolution";
    private static final String AGENT_VERSION = "1.0.0";
    
    // Patterns for detecting software bug related content
    private static final Pattern BUG_PATTERN = Pattern.compile(
        "\\b(bug|error|exception|crash|failure|issue|problem|defect)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern CODE_PATTERN = Pattern.compile(
        "\\b(code|program|software|application|function|method|class)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getAgentId() {
        return AGENT_ID;
    }
    
    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }
    
    @Override
    public String getAgentDescription() {
        return AGENT_DESCRIPTION;
    }
    
    @Override
    public String getAgentVersion() {
        return AGENT_VERSION;
    }
    
    @Override
    public Map<String, String> getCapabilities() {
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("bug_analysis", "Analyze software bugs and error messages");
        capabilities.put("code_debugging", "Help debug code issues and problems");
        capabilities.put("error_diagnosis", "Diagnose software errors and exceptions");
        capabilities.put("stack_trace_analysis", "Analyze stack traces and error logs");
        capabilities.put("code_review", "Review code for potential bugs and issues");
        capabilities.put("fix_suggestions", "Suggest fixes and solutions for code problems");
        return capabilities;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Software Bug Assistant Agent processing request: {}", request.getSessionId());
                
                String prompt = getPromptFromRequest(request);
                String response = analyzeBugRequest(prompt);
                
                ModelResponse modelResponse = new ModelResponse();
                modelResponse.setResponseId(java.util.UUID.randomUUID().toString());
                modelResponse.setSessionId(request.getSessionId());
                modelResponse.setModelName(request.getModelName());
                modelResponse.setResponse(response);
                modelResponse.setStatus(ResponseStatus.SUCCESS);
                
                ResponseMetadata metadata = new ResponseMetadata();
                metadata.setModel(request.getModelName());
                metadata.setProvider(getAgentName());
                metadata.setProcessingTimeMs(System.currentTimeMillis());
                modelResponse.setMetadata(metadata);
                
                return modelResponse;
                        
            } catch (Exception e) {
                logger.error("Error processing software bug assistant request", e);
                return createErrorResponse(request, "Error in bug analysis: " + e.getMessage());
            }
        });
    }
    
    @Override
    public boolean canHandle(ModelRequest request) {
        String prompt = getPromptFromRequest(request).toLowerCase();
        
        // Check for bug/error related keywords
        boolean hasBugKeywords = BUG_PATTERN.matcher(prompt).find();
        boolean hasCodeKeywords = CODE_PATTERN.matcher(prompt).find();
        
        // Check for debugging keywords
        boolean hasDebuggingKeywords = prompt.contains("debug") || 
                                     prompt.contains("fix") || 
                                     prompt.contains("solve") ||
                                     prompt.contains("troubleshoot");
        
        return hasBugKeywords || (hasCodeKeywords && hasDebuggingKeywords);
    }
    
    @Override
    public int getPriority() {
        // High priority for software bug specific requests
        return 10;
    }
    
    private String analyzeBugRequest(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        StringBuilder response = new StringBuilder();
        response.append("🐛 **Software Bug Assistant Analysis**\n\n");
        
        // Analyze the type of request
        if (lowerPrompt.contains("exception") || lowerPrompt.contains("error")) {
            response.append("**Error/Exception Analysis Request Detected**\n");
            response.append("I can help you analyze and fix software errors. Here's what I can do:\n\n");
            
            response.append("🔍 **Error Analysis Capabilities:**\n");
            response.append("- Parse and analyze error messages and stack traces\n");
            response.append("- Identify root causes of exceptions\n");
            response.append("- Suggest debugging strategies and tools\n");
            response.append("- Provide code fixes and workarounds\n\n");
            
            response.append("📋 **Required Information:**\n");
            response.append("- Complete error message or stack trace\n");
            response.append("- Relevant code snippet where the error occurs\n");
            response.append("- Programming language and framework details\n");
            response.append("- Steps to reproduce the issue\n\n");
            
        } else if (lowerPrompt.contains("bug") || lowerPrompt.contains("issue")) {
            response.append("**Bug Analysis Request Detected**\n");
            response.append("I can help you identify and fix software bugs. Here's what I can do:\n\n");
            
            response.append("🐛 **Bug Analysis Capabilities:**\n");
            response.append("- Analyze bug reports and descriptions\n");
            response.append("- Review code for potential issues\n");
            response.append("- Suggest debugging approaches\n");
            response.append("- Provide fix recommendations\n\n");
            
        } else if (lowerPrompt.contains("debug") || lowerPrompt.contains("fix")) {
            response.append("**Debugging/Fixing Request Detected**\n");
            response.append("I can help you debug and fix code issues. Here's what I can do:\n\n");
            
            response.append("🔧 **Debugging Capabilities:**\n");
            response.append("- Code review and analysis\n");
            response.append("- Logic error identification\n");
            response.append("- Performance issue detection\n");
            response.append("- Best practice recommendations\n\n");
            
        } else {
            response.append("**General Software Bug Request**\n");
            response.append("I'm your specialized software bug assistant. I can help with:\n\n");
            
            response.append("🎯 **My Specializations:**\n");
            response.append("- Software bug analysis and debugging\n");
            response.append("- Error message interpretation\n");
            response.append("- Code problem diagnosis\n");
            response.append("- Fix suggestion and implementation\n");
            response.append("- Code review and quality assessment\n\n");
        }
        
        response.append("💡 **Next Steps:**\n");
        response.append("Please provide the error message, code snippet, or bug description. ");
        response.append("I can help analyze the issue and suggest solutions.\n\n");
        
        response.append("**Your Request:** ").append(prompt).append("\n\n");
        response.append("How can I help you debug this software issue?");
        
        return response.toString();
    }
    
    private String getPromptFromRequest(ModelRequest request) {
        if (request.getUserMessages() != null && !request.getUserMessages().isEmpty()) {
            return request.getUserMessages().get(0);
        }
        return request.getSystemMessage();
    }
    
    private ModelResponse createErrorResponse(ModelRequest request, String errorMessage) {
        ModelResponse errorResponse = new ModelResponse();
        errorResponse.setResponseId(java.util.UUID.randomUUID().toString());
        errorResponse.setSessionId(request.getSessionId());
        errorResponse.setModelName(request.getModelName());
        errorResponse.setResponse("Error: " + errorMessage);
        errorResponse.setStatus(ResponseStatus.ERROR);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        errorResponse.setMetadata(metadata);
        
        return errorResponse;
    }
}
