package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import com.agentic.framework.model.ModelResponse.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * Time Series Forecasting Agent
 * Specialized agent for time series analysis and forecasting
 * Based on Google ADK time series forecasting patterns
 */
@Component
public class TimeSeriesForecastingAgent implements Agent {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeSeriesForecastingAgent.class);
    
    private static final String AGENT_ID = "time-series-forecasting-agent";
    private static final String AGENT_NAME = "Time Series Forecasting Agent";
    private static final String AGENT_DESCRIPTION = "Specialized agent for time series analysis, forecasting, and trend prediction";
    private static final String AGENT_VERSION = "1.0.0";
    
    // Patterns for detecting time series related content
    private static final Pattern TIME_SERIES_PATTERN = Pattern.compile(
        "\\b(time\\s*series|forecast|prediction|trend|seasonal|temporal|historical\\s*data)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern DATA_PATTERN = Pattern.compile(
        "\\b(data\\s*points|values|measurements|observations|records)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getAgentId() {
        return AGENT_ID;
    }
    
    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }
    
    @Override
    public String getAgentDescription() {
        return AGENT_DESCRIPTION;
    }
    
    @Override
    public String getAgentVersion() {
        return AGENT_VERSION;
    }
    
    @Override
    public Map<String, String> getCapabilities() {
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("time_series_analysis", "Analyze time series data for patterns and trends");
        capabilities.put("forecasting", "Generate predictions for future values");
        capabilities.put("seasonality_detection", "Detect seasonal patterns in data");
        capabilities.put("trend_analysis", "Identify and analyze trends in time series data");
        capabilities.put("anomaly_detection", "Detect anomalies and outliers in time series");
        capabilities.put("statistical_modeling", "Apply statistical models for time series analysis");
        return capabilities;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Time Series Forecasting Agent processing request: {}", request.getSessionId());
                
                String prompt = getPromptFromRequest(request);
                String response = analyzeTimeSeriesRequest(prompt);
                
                ModelResponse modelResponse = new ModelResponse();
                modelResponse.setResponseId(java.util.UUID.randomUUID().toString());
                modelResponse.setSessionId(request.getSessionId());
                modelResponse.setModelName(request.getModelName());
                modelResponse.setResponse(response);
                modelResponse.setStatus(ResponseStatus.SUCCESS);
                
                ResponseMetadata metadata = new ResponseMetadata();
                metadata.setModel(request.getModelName());
                metadata.setProvider(getAgentName());
                metadata.setProcessingTimeMs(System.currentTimeMillis());
                modelResponse.setMetadata(metadata);
                
                return modelResponse;
                        
            } catch (Exception e) {
                logger.error("Error processing time series forecasting request", e);
                return createErrorResponse(request, "Error in time series analysis: " + e.getMessage());
            }
        });
    }
    
    @Override
    public boolean canHandle(ModelRequest request) {
        String prompt = getPromptFromRequest(request).toLowerCase();
        
        // Check for time series related keywords
        boolean hasTimeSeriesKeywords = TIME_SERIES_PATTERN.matcher(prompt).find();
        boolean hasDataKeywords = DATA_PATTERN.matcher(prompt).find();
        
        // Check for forecasting/prediction keywords
        boolean hasForecastingKeywords = prompt.contains("forecast") || 
                                       prompt.contains("predict") || 
                                       prompt.contains("future") ||
                                       prompt.contains("trend");
        
        return hasTimeSeriesKeywords || (hasDataKeywords && hasForecastingKeywords);
    }
    
    @Override
    public int getPriority() {
        // High priority for time series specific requests
        return 10;
    }
    
    private String analyzeTimeSeriesRequest(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        StringBuilder response = new StringBuilder();
        response.append("🔮 **Time Series Forecasting Analysis**\n\n");
        
        // Analyze the type of request
        if (lowerPrompt.contains("forecast") || lowerPrompt.contains("predict")) {
            response.append("**Forecasting Request Detected**\n");
            response.append("I can help you with time series forecasting. Here's what I can do:\n\n");
            
            response.append("📊 **Forecasting Capabilities:**\n");
            response.append("- Generate point forecasts and prediction intervals\n");
            response.append("- Handle seasonal and trend components\n");
            response.append("- Apply various forecasting models (ARIMA, Exponential Smoothing, etc.)\n");
            response.append("- Provide confidence intervals and uncertainty quantification\n\n");
            
            response.append("📈 **Required Information:**\n");
            response.append("- Historical time series data\n");
            response.append("- Forecast horizon (how far into the future)\n");
            response.append("- Seasonality patterns (if known)\n");
            response.append("- Data frequency (daily, weekly, monthly, etc.)\n\n");
            
        } else if (lowerPrompt.contains("analyze") || lowerPrompt.contains("pattern")) {
            response.append("**Time Series Analysis Request Detected**\n");
            response.append("I can help you analyze time series patterns. Here's what I can do:\n\n");
            
            response.append("🔍 **Analysis Capabilities:**\n");
            response.append("- Trend analysis and decomposition\n");
            response.append("- Seasonality detection and analysis\n");
            response.append("- Stationarity testing\n");
            response.append("- Autocorrelation analysis\n");
            response.append("- Anomaly detection\n\n");
            
        } else {
            response.append("**General Time Series Request**\n");
            response.append("I'm your specialized time series forecasting agent. I can help with:\n\n");
            
            response.append("🎯 **My Specializations:**\n");
            response.append("- Time series forecasting and predictions\n");
            response.append("- Trend analysis and pattern recognition\n");
            response.append("- Seasonal decomposition and analysis\n");
            response.append("- Statistical modeling for time series data\n");
            response.append("- Anomaly detection in temporal data\n\n");
        }
        
        response.append("💡 **Next Steps:**\n");
        response.append("Please provide your time series data and specify what you'd like to forecast or analyze. ");
        response.append("I can work with various data formats and time frequencies.\n\n");
        
        response.append("**Your Request:** ").append(prompt).append("\n\n");
        response.append("How can I help you with your time series analysis?");
        
        return response.toString();
    }
    
    private String getPromptFromRequest(ModelRequest request) {
        if (request.getUserMessages() != null && !request.getUserMessages().isEmpty()) {
            return request.getUserMessages().get(0);
        }
        return request.getSystemMessage();
    }
    
    private ModelResponse createErrorResponse(ModelRequest request, String errorMessage) {
        ModelResponse errorResponse = new ModelResponse();
        errorResponse.setResponseId(java.util.UUID.randomUUID().toString());
        errorResponse.setSessionId(request.getSessionId());
        errorResponse.setModelName(request.getModelName());
        errorResponse.setResponse("Error: " + errorMessage);
        errorResponse.setStatus(ResponseStatus.ERROR);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        errorResponse.setMetadata(metadata);
        
        return errorResponse;
    }
}
