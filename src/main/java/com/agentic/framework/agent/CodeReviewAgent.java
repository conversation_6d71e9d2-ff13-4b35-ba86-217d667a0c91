package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import com.agentic.framework.model.ModelResponse.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * Code Review Agent
 * Specialized agent for code review, optimization, and best practices
 */
@Component
public class CodeReviewAgent implements Agent {
    
    private static final Logger logger = LoggerFactory.getLogger(CodeReviewAgent.class);
    
    private static final String AGENT_ID = "code-review-agent";
    private static final String AGENT_NAME = "Code Review Agent";
    private static final String AGENT_DESCRIPTION = "Specialized agent for code review, optimization, and best practices analysis";
    private static final String AGENT_VERSION = "1.0.0";
    
    // Patterns for detecting code review related content
    private static final Pattern CODE_REVIEW_PATTERN = Pattern.compile(
        "\\b(code\\s*review|review\\s*code|code\\s*quality|refactor|optimize|best\\s*practices)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern CODE_PATTERN = Pattern.compile(
        "\\b(code|function|method|class|program|algorithm|implementation)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getAgentId() {
        return AGENT_ID;
    }
    
    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }
    
    @Override
    public String getAgentDescription() {
        return AGENT_DESCRIPTION;
    }
    
    @Override
    public String getAgentVersion() {
        return AGENT_VERSION;
    }
    
    @Override
    public Map<String, String> getCapabilities() {
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("code_review", "Review code for quality and best practices");
        capabilities.put("code_optimization", "Suggest code optimizations and improvements");
        capabilities.put("best_practices", "Identify and suggest best practices");
        capabilities.put("code_quality", "Assess code quality and maintainability");
        capabilities.put("refactoring", "Suggest refactoring opportunities");
        capabilities.put("security_analysis", "Identify potential security issues");
        return capabilities;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Code Review Agent processing request: {}", request.getSessionId());
                
                String prompt = getPromptFromRequest(request);
                String response = analyzeCodeReviewRequest(prompt);
                
                ModelResponse modelResponse = new ModelResponse();
                modelResponse.setResponseId(java.util.UUID.randomUUID().toString());
                modelResponse.setSessionId(request.getSessionId());
                modelResponse.setModelName(request.getModelName());
                modelResponse.setResponse(response);
                modelResponse.setStatus(ResponseStatus.SUCCESS);
                
                ResponseMetadata metadata = new ResponseMetadata();
                metadata.setModel(request.getModelName());
                metadata.setProvider(getAgentName());
                metadata.setProcessingTimeMs(System.currentTimeMillis());
                modelResponse.setMetadata(metadata);
                
                return modelResponse;
                        
            } catch (Exception e) {
                logger.error("Error processing code review request", e);
                return createErrorResponse(request, "Error in code review: " + e.getMessage());
            }
        });
    }
    
    @Override
    public boolean canHandle(ModelRequest request) {
        String prompt = getPromptFromRequest(request).toLowerCase();
        
        // Check for code review related keywords
        boolean hasCodeReviewKeywords = CODE_REVIEW_PATTERN.matcher(prompt).find();
        boolean hasCodeKeywords = CODE_PATTERN.matcher(prompt).find();
        
        // Check for specific review keywords
        boolean hasSpecificKeywords = prompt.contains("quality") || 
                                    prompt.contains("optimize") || 
                                    prompt.contains("refactor") ||
                                    prompt.contains("improve");
        
        return hasCodeReviewKeywords || (hasCodeKeywords && hasSpecificKeywords);
    }
    
    @Override
    public int getPriority() {
        // High priority for code review specific requests
        return 10;
    }
    
    private String analyzeCodeReviewRequest(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        StringBuilder response = new StringBuilder();
        response.append("🔍 **Code Review Agent**\n\n");
        
        // Analyze the type of request
        if (lowerPrompt.contains("review") || lowerPrompt.contains("quality")) {
            response.append("**Code Review Request Detected**\n");
            response.append("I can help you review code for quality and best practices. Here's what I can do:\n\n");
            
            response.append("📋 **Review Capabilities:**\n");
            response.append("- Analyze code structure and organization\n");
            response.append("- Identify potential bugs and issues\n");
            response.append("- Check for coding standards compliance\n");
            response.append("- Assess readability and maintainability\n");
            response.append("- Suggest improvements and optimizations\n\n");
            
            response.append("📝 **Required Information:**\n");
            response.append("- Code snippet or file to review\n");
            response.append("- Programming language and framework\n");
            response.append("- Specific concerns or focus areas\n");
            response.append("- Context and requirements\n\n");
            
        } else if (lowerPrompt.contains("optimize") || lowerPrompt.contains("performance")) {
            response.append("**Code Optimization Request Detected**\n");
            response.append("I can help you optimize code for better performance. Here's what I can do:\n\n");
            
            response.append("⚡ **Optimization Capabilities:**\n");
            response.append("- Identify performance bottlenecks\n");
            response.append("- Suggest algorithmic improvements\n");
            response.append("- Optimize memory usage\n");
            response.append("- Improve execution efficiency\n\n");
            
        } else if (lowerPrompt.contains("refactor") || lowerPrompt.contains("restructure")) {
            response.append("**Code Refactoring Request Detected**\n");
            response.append("I can help you refactor code for better structure. Here's what I can do:\n\n");
            
            response.append("🔄 **Refactoring Capabilities:**\n");
            response.append("- Identify refactoring opportunities\n");
            response.append("- Suggest structural improvements\n");
            response.append("- Extract methods and classes\n");
            response.append("- Improve code organization\n\n");
            
        } else if (lowerPrompt.contains("security") || lowerPrompt.contains("vulnerability")) {
            response.append("**Security Analysis Request Detected**\n");
            response.append("I can help you identify security issues in code. Here's what I can do:\n\n");
            
            response.append("🔒 **Security Analysis Capabilities:**\n");
            response.append("- Identify security vulnerabilities\n");
            response.append("- Check for common security issues\n");
            response.append("- Suggest security best practices\n");
            response.append("- Review authentication and authorization\n\n");
            
        } else {
            response.append("**General Code Review Request**\n");
            response.append("I'm your specialized code review agent. I can help with:\n\n");
            
            response.append("🎯 **My Specializations:**\n");
            response.append("- Code quality assessment and review\n");
            response.append("- Performance optimization suggestions\n");
            response.append("- Best practices identification\n");
            response.append("- Code refactoring recommendations\n");
            response.append("- Security vulnerability detection\n\n");
        }
        
        response.append("💡 **Next Steps:**\n");
        response.append("Please provide the code you'd like me to review. ");
        response.append("I can analyze it for quality, performance, security, and best practices.\n\n");
        
        response.append("**Your Request:** ").append(prompt).append("\n\n");
        response.append("How can I help you improve your code?");
        
        return response.toString();
    }
    
    private String getPromptFromRequest(ModelRequest request) {
        if (request.getUserMessages() != null && !request.getUserMessages().isEmpty()) {
            return request.getUserMessages().get(0);
        }
        return request.getSystemMessage();
    }
    
    private ModelResponse createErrorResponse(ModelRequest request, String errorMessage) {
        ModelResponse errorResponse = new ModelResponse();
        errorResponse.setResponseId(java.util.UUID.randomUUID().toString());
        errorResponse.setSessionId(request.getSessionId());
        errorResponse.setModelName(request.getModelName());
        errorResponse.setResponse("Error: " + errorMessage);
        errorResponse.setStatus(ResponseStatus.ERROR);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        errorResponse.setMetadata(metadata);
        
        return errorResponse;
    }
}
