package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Core Agent interface for the Agentic Framework
 * Defines the contract that all agents must implement
 * Based on Google ADK patterns for agent development
 */
public interface Agent {
    
    /**
     * Get the unique identifier for this agent
     * @return agent ID
     */
    String getAgentId();
    
    /**
     * Get the display name for this agent
     * @return agent name
     */
    String getAgentName();
    
    /**
     * Get the description of what this agent does
     * @return agent description
     */
    String getAgentDescription();
    
    /**
     * Get the version of this agent
     * @return agent version
     */
    String getAgentVersion();
    
    /**
     * Get the capabilities of this agent
     * @return map of capability names to descriptions
     */
    Map<String, String> getCapabilities();
    
    /**
     * Process a request using this agent
     * @param request the model request to process
     * @return future containing the model response
     */
    CompletableFuture<ModelResponse> processRequest(ModelRequest request);
    
    /**
     * Check if this agent can handle the given request
     * @param request the request to check
     * @return true if the agent can handle this request
     */
    boolean canHandle(ModelRequest request);
    
    /**
     * Get the priority of this agent for handling requests
     * Higher priority agents are preferred when multiple agents can handle a request
     * @return priority value (higher = more preferred)
     */
    default int getPriority() {
        return 1;
    }
    
    /**
     * Initialize the agent with configuration
     * @param config agent configuration
     */
    default void initialize(Map<String, Object> config) {
        // Default implementation does nothing
    }
    
    /**
     * Clean up resources when the agent is being shut down
     */
    default void cleanup() {
        // Default implementation does nothing
    }
}
