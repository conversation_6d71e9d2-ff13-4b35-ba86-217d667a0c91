package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import com.agentic.framework.model.ModelResponse.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * General Purpose Agent that can handle various types of requests
 * Acts as a router to specialized agents when appropriate
 * Based on Google ADK patterns for general-purpose agents
 */
@Component
public class GeneralPurposeAgent implements Agent {
    
    private static final Logger logger = LoggerFactory.getLogger(GeneralPurposeAgent.class);
    
    @Autowired
    private AgentRegistry agentRegistry;
    
    private static final String AGENT_ID = "general-purpose-agent";
    private static final String AGENT_NAME = "General Purpose Agent";
    private static final String AGENT_DESCRIPTION = "A versatile agent that can handle various types of requests and route to specialized agents when needed";
    private static final String AGENT_VERSION = "1.0.0";
    
    @Override
    public String getAgentId() {
        return AGENT_ID;
    }
    
    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }
    
    @Override
    public String getAgentDescription() {
        return AGENT_DESCRIPTION;
    }
    
    @Override
    public String getAgentVersion() {
        return AGENT_VERSION;
    }
    
    @Override
    public Map<String, String> getCapabilities() {
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("general_conversation", "Handle general conversation and questions");
        capabilities.put("task_routing", "Route tasks to appropriate specialized agents");
        capabilities.put("context_management", "Manage conversation context and memory");
        capabilities.put("multi_agent_coordination", "Coordinate between multiple specialized agents");
        return capabilities;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("General Purpose Agent processing request: {}", request.getSessionId());
                
                // Check if we should route to a specialized agent
                Agent specializedAgent = findSpecializedAgent(request);
                
                if (specializedAgent != null && !specializedAgent.getAgentId().equals(getAgentId())) {
                    logger.info("Routing request to specialized agent: {}", specializedAgent.getAgentName());
                    return specializedAgent.processRequest(request).join();
                }
                
                // Handle the request directly
                return handleGeneralRequest(request);
                
            } catch (Exception e) {
                logger.error("Error processing request in General Purpose Agent", e);
                return createErrorResponse(request, "Error processing request: " + e.getMessage());
            }
        });
    }
    
    @Override
    public boolean canHandle(ModelRequest request) {
        // General purpose agent can handle any request
        return true;
    }
    
    @Override
    public int getPriority() {
        // Lower priority than specialized agents
        return 1;
    }
    
    private Agent findSpecializedAgent(ModelRequest request) {
        String prompt = getPromptFromRequest(request);
        if (prompt != null) {
            prompt = prompt.toLowerCase();
        } else {
            return null; // No prompt to analyze
        }
        
        // Check for time series forecasting keywords
        if (containsKeywords(prompt, "forecast", "prediction", "time series", "trend", "future")) {
            return agentRegistry.getAgent("time-series-forecasting-agent");
        }
        
        // Check for software bug keywords
        if (containsKeywords(prompt, "bug", "error", "exception", "debug", "fix", "issue", "problem")) {
            return agentRegistry.getAgent("software-bug-assistant-agent");
        }
        
        // Check for data analysis keywords
        if (containsKeywords(prompt, "analyze", "data", "statistics", "chart", "graph", "insights")) {
            return agentRegistry.getAgent("data-analysis-agent");
        }
        
        // Check for code review keywords
        if (containsKeywords(prompt, "code review", "code quality", "refactor", "optimize", "best practices")) {
            return agentRegistry.getAgent("code-review-agent");
        }
        
        return null;
    }
    
    private boolean containsKeywords(String text, String... keywords) {
        for (String keyword : keywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    private ModelResponse handleGeneralRequest(ModelRequest request) {
        String prompt = getPromptFromRequest(request);
        
        // Generate a general response
        String response = "I'm a general purpose agent that can help you with various tasks. " +
                         "I can route your request to specialized agents for better assistance. " +
                         "For example:\n" +
                         "- Time series forecasting and predictions\n" +
                         "- Software bug analysis and debugging\n" +
                         "- Data analysis and insights\n" +
                         "- Code review and optimization\n\n" +
                         "Your request: " + prompt + "\n\n" +
                         "How can I help you today?";
        
        ModelResponse modelResponse = new ModelResponse();
        modelResponse.setResponseId(java.util.UUID.randomUUID().toString());
        modelResponse.setSessionId(request.getSessionId());
        modelResponse.setModelName(request.getModelName());
        modelResponse.setResponse(response);
        modelResponse.setStatus(ResponseStatus.SUCCESS);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        modelResponse.setMetadata(metadata);
        
        return modelResponse;
    }
    
    private String getPromptFromRequest(ModelRequest request) {
        if (request.getUserMessages() != null && !request.getUserMessages().isEmpty()) {
            return request.getUserMessages().get(0);
        }
        return request.getSystemMessage();
    }
    
    private ModelResponse createErrorResponse(ModelRequest request, String errorMessage) {
        ModelResponse errorResponse = new ModelResponse();
        errorResponse.setResponseId(java.util.UUID.randomUUID().toString());
        errorResponse.setSessionId(request.getSessionId());
        errorResponse.setModelName(request.getModelName());
        errorResponse.setResponse("Error: " + errorMessage);
        errorResponse.setStatus(ResponseStatus.ERROR);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        errorResponse.setMetadata(metadata);
        
        return errorResponse;
    }
}
