package com.agentic.framework.agent;

import com.agentic.framework.model.ModelRequest;
import com.agentic.framework.model.ModelResponse;
import com.agentic.framework.model.ResponseMetadata;
import com.agentic.framework.model.ModelResponse.ResponseStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * Data Analysis Agent
 * Specialized agent for data analysis, insights, and statistical analysis
 */
@Component
public class DataAnalysisAgent implements Agent {
    
    private static final Logger logger = LoggerFactory.getLogger(DataAnalysisAgent.class);
    
    private static final String AGENT_ID = "data-analysis-agent";
    private static final String AGENT_NAME = "Data Analysis Agent";
    private static final String AGENT_DESCRIPTION = "Specialized agent for data analysis, statistical insights, and data visualization";
    private static final String AGENT_VERSION = "1.0.0";
    
    // Patterns for detecting data analysis related content
    private static final Pattern DATA_PATTERN = Pattern.compile(
        "\\b(data|dataset|statistics|analysis|insights|chart|graph|visualization)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern ANALYZE_PATTERN = Pattern.compile(
        "\\b(analyze|examine|study|investigate|explore|understand|pattern|trend)\\b", 
        Pattern.CASE_INSENSITIVE
    );
    
    @Override
    public String getAgentId() {
        return AGENT_ID;
    }
    
    @Override
    public String getAgentName() {
        return AGENT_NAME;
    }
    
    @Override
    public String getAgentDescription() {
        return AGENT_DESCRIPTION;
    }
    
    @Override
    public String getAgentVersion() {
        return AGENT_VERSION;
    }
    
    @Override
    public Map<String, String> getCapabilities() {
        Map<String, String> capabilities = new HashMap<>();
        capabilities.put("data_analysis", "Analyze datasets and extract insights");
        capabilities.put("statistical_analysis", "Perform statistical analysis and hypothesis testing");
        capabilities.put("data_visualization", "Create charts, graphs, and visualizations");
        capabilities.put("pattern_recognition", "Identify patterns and trends in data");
        capabilities.put("correlation_analysis", "Analyze correlations between variables");
        capabilities.put("data_cleaning", "Clean and preprocess data for analysis");
        return capabilities;
    }
    
    @Override
    public CompletableFuture<ModelResponse> processRequest(ModelRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Data Analysis Agent processing request: {}", request.getSessionId());
                
                String prompt = getPromptFromRequest(request);
                String response = analyzeDataRequest(prompt);
                
                ModelResponse modelResponse = new ModelResponse();
                modelResponse.setResponseId(java.util.UUID.randomUUID().toString());
                modelResponse.setSessionId(request.getSessionId());
                modelResponse.setModelName(request.getModelName());
                modelResponse.setResponse(response);
                modelResponse.setStatus(ResponseStatus.SUCCESS);
                
                ResponseMetadata metadata = new ResponseMetadata();
                metadata.setModel(request.getModelName());
                metadata.setProvider(getAgentName());
                metadata.setProcessingTimeMs(System.currentTimeMillis());
                modelResponse.setMetadata(metadata);
                
                return modelResponse;
                        
            } catch (Exception e) {
                logger.error("Error processing data analysis request", e);
                return createErrorResponse(request, "Error in data analysis: " + e.getMessage());
            }
        });
    }
    
    @Override
    public boolean canHandle(ModelRequest request) {
        String prompt = getPromptFromRequest(request).toLowerCase();
        
        // Check for data analysis related keywords
        boolean hasDataKeywords = DATA_PATTERN.matcher(prompt).find();
        boolean hasAnalyzeKeywords = ANALYZE_PATTERN.matcher(prompt).find();
        
        // Check for specific analysis keywords
        boolean hasSpecificKeywords = prompt.contains("insights") || 
                                    prompt.contains("chart") || 
                                    prompt.contains("graph") ||
                                    prompt.contains("correlation");
        
        return hasDataKeywords || (hasAnalyzeKeywords && hasSpecificKeywords);
    }
    
    @Override
    public int getPriority() {
        // High priority for data analysis specific requests
        return 10;
    }
    
    private String analyzeDataRequest(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        StringBuilder response = new StringBuilder();
        response.append("📊 **Data Analysis Agent**\n\n");
        
        // Analyze the type of request
        if (lowerPrompt.contains("insights") || lowerPrompt.contains("understand")) {
            response.append("**Data Insights Request Detected**\n");
            response.append("I can help you extract meaningful insights from your data. Here's what I can do:\n\n");
            
            response.append("🔍 **Insights Capabilities:**\n");
            response.append("- Identify key patterns and trends\n");
            response.append("- Extract actionable insights\n");
            response.append("- Perform exploratory data analysis\n");
            response.append("- Generate summary statistics\n\n");
            
            response.append("📋 **Required Information:**\n");
            response.append("- Dataset or data description\n");
            response.append("- Specific questions or objectives\n");
            response.append("- Data format and structure\n");
            response.append("- Context and domain information\n\n");
            
        } else if (lowerPrompt.contains("chart") || lowerPrompt.contains("graph") || lowerPrompt.contains("visualization")) {
            response.append("**Data Visualization Request Detected**\n");
            response.append("I can help you create effective data visualizations. Here's what I can do:\n\n");
            
            response.append("📈 **Visualization Capabilities:**\n");
            response.append("- Recommend appropriate chart types\n");
            response.append("- Design effective visualizations\n");
            response.append("- Create interactive charts and graphs\n");
            response.append("- Optimize visual presentation\n\n");
            
        } else if (lowerPrompt.contains("correlation") || lowerPrompt.contains("relationship")) {
            response.append("**Correlation Analysis Request Detected**\n");
            response.append("I can help you analyze relationships between variables. Here's what I can do:\n\n");
            
            response.append("🔗 **Correlation Analysis Capabilities:**\n");
            response.append("- Calculate correlation coefficients\n");
            response.append("- Identify causal relationships\n");
            response.append("- Perform regression analysis\n");
            response.append("- Test statistical significance\n\n");
            
        } else {
            response.append("**General Data Analysis Request**\n");
            response.append("I'm your specialized data analysis agent. I can help with:\n\n");
            
            response.append("🎯 **My Specializations:**\n");
            response.append("- Data analysis and insights extraction\n");
            response.append("- Statistical analysis and testing\n");
            response.append("- Data visualization and charting\n");
            response.append("- Pattern recognition and trend analysis\n");
            response.append("- Data cleaning and preprocessing\n\n");
        }
        
        response.append("💡 **Next Steps:**\n");
        response.append("Please provide your data or describe what you'd like to analyze. ");
        response.append("I can help you understand patterns, create visualizations, and extract insights.\n\n");
        
        response.append("**Your Request:** ").append(prompt).append("\n\n");
        response.append("How can I help you analyze your data?");
        
        return response.toString();
    }
    
    private String getPromptFromRequest(ModelRequest request) {
        if (request.getUserMessages() != null && !request.getUserMessages().isEmpty()) {
            return request.getUserMessages().get(0);
        }
        return request.getSystemMessage();
    }
    
    private ModelResponse createErrorResponse(ModelRequest request, String errorMessage) {
        ModelResponse errorResponse = new ModelResponse();
        errorResponse.setResponseId(java.util.UUID.randomUUID().toString());
        errorResponse.setSessionId(request.getSessionId());
        errorResponse.setModelName(request.getModelName());
        errorResponse.setResponse("Error: " + errorMessage);
        errorResponse.setStatus(ResponseStatus.ERROR);
        
        ResponseMetadata metadata = new ResponseMetadata();
        metadata.setModel(request.getModelName());
        metadata.setProvider(getAgentName());
        metadata.setProcessingTimeMs(System.currentTimeMillis());
        errorResponse.setMetadata(metadata);
        
        return errorResponse;
    }
}
