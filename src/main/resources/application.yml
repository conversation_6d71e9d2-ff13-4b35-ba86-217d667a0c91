server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: agentic-framework
  profiles:
    active: dev
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
  validation:
    enabled: true
  task:
    execution:
      pool:
        core-size: 8
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s

# MCP Server Configuration
mcp:
  server:
    host: localhost
    port: 8081
    max-connections: 100
    read-timeout: 30000
    write-timeout: 30000
    enable-ssl: false

  # Model Provider Configurations
  models:
    openai:
      api-key: ********************************************************************************************************************************************************************
      base-url: https://api.openai.com/v1
    anthropic:
      api-key: ************************************************************************************************************
      base-url: https://api.anthropic.com/v1
    local:
      base-url: http://localhost:11434

# Context Management Configuration
context:
  storage:
    max-contexts-per-user: 100
    max-context-size: 10000
    cleanup-interval: 3600000  # 1 hour in milliseconds
  memory:
    max-memories-per-user: 1000
    memory-ttl: 86400000  # 24 hours in milliseconds

# Session Management Configuration
session:
  timeout: 3600000  # 1 hour in milliseconds
  max-sessions-per-user: 10
  cleanup-interval: 1800000  # 30 minutes in milliseconds

# Logging Configuration
logging:
  level:
    com.agentic.framework: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/agentic-framework.log
    max-size: 100MB
    max-history: 30

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# Security Configuration (Basic)
security:
  basic:
    enabled: false
  oauth2:
    enabled: false

# Performance Configuration
# (Moved to main spring section above)
