<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agentic Framework - API Testing Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .api-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        .response-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .tool-card {
            margin-bottom: 1rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                    <div class="container-fluid">
                        <a class="navbar-brand" href="#">
                            <i class="fas fa-robot me-2"></i>
                            Agentic Framework
                        </a>
                        <div class="navbar-nav ms-auto">
                            <span class="navbar-text">
                                <span class="status-indicator status-online" id="statusIndicator"></span>
                                <span id="statusText">Online</span>
                            </span>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            API Endpoints
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="#conversation" class="list-group-item list-group-item-action" data-bs-toggle="collapse">
                                <i class="fas fa-comments me-2"></i>
                                Conversation
                            </a>
                            <a href="#tools" class="list-group-item list-group-item-action" data-bs-toggle="collapse">
                                <i class="fas fa-tools me-2"></i>
                                MCP Tools
                            </a>
                            <a href="#agents" class="list-group-item list-group-item-action" data-bs-toggle="collapse">
                                <i class="fas fa-robot me-2"></i>
                                Agents
                            </a>
                            <a href="#models" class="list-group-item list-group-item-action" data-bs-toggle="collapse">
                                <i class="fas fa-brain me-2"></i>
                                Models
                            </a>
                            <a href="#health" class="list-group-item list-group-item-action" data-bs-toggle="collapse">
                                <i class="fas fa-heartbeat me-2"></i>
                                Health & Metrics
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="baseUrl" class="form-label">Base URL</label>
                            <input type="text" class="form-control" id="baseUrl" value="http://localhost:8080">
                        </div>
                        <div class="mb-3">
                            <label for="userId" class="form-label">User ID</label>
                            <input type="text" class="form-control" id="userId" value="test-user-123">
                        </div>
                        <div class="mb-3">
                            <label for="sessionId" class="form-label">Session ID</label>
                            <input type="text" class="form-control" id="sessionId" value="">
                            <button class="btn btn-sm btn-outline-primary mt-1" onclick="generateSessionId()">
                                <i class="fas fa-random me-1"></i>
                                Generate
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Conversation Section -->
                <div class="api-section" id="conversation">
                    <h4><i class="fas fa-comments me-2"></i>Conversation Management</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Send Message</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="systemMessage" class="form-label">System Message</label>
                                        <textarea class="form-control" id="systemMessage" rows="2">You are a helpful AI assistant</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="userMessage" class="form-label">User Message</label>
                                        <textarea class="form-control" id="userMessage" rows="3">Hello, how are you?</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="agentId" class="form-label">Agent ID (Optional)</label>
                                        <input type="text" class="form-control" id="agentId" placeholder="Auto-select if empty">
                                    </div>
                                    <button class="btn btn-primary" onclick="sendMessage()">
                                        <i class="fas fa-paper-plane me-1"></i>
                                        Send Message
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Response</h6>
                                </div>
                                <div class="card-body">
                                    <div class="response-area" id="messageResponse">Response will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Get Context</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="contextLimit" class="form-label">Limit</label>
                                        <input type="number" class="form-control" id="contextLimit" value="10">
                                    </div>
                                    <button class="btn btn-outline-primary" onclick="getContext()">
                                        <i class="fas fa-history me-1"></i>
                                        Get Context
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Search Memories</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="memoryQuery" class="form-label">Query</label>
                                        <input type="text" class="form-control" id="memoryQuery" placeholder="Search term">
                                    </div>
                                    <button class="btn btn-outline-primary" onclick="searchMemories()">
                                        <i class="fas fa-search me-1"></i>
                                        Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tools Section -->
                <div class="api-section" id="tools">
                    <h4><i class="fas fa-tools me-2"></i>MCP Tools</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Execute Tool</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="toolName" class="form-label">Tool Name</label>
                                        <select class="form-select" id="toolName">
                                            <option value="web_search">web_search</option>
                                            <option value="file_read">file_read</option>
                                            <option value="calculator">calculator</option>
                                            <option value="weather">weather</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="toolParams" class="form-label">Parameters (JSON)</label>
                                        <textarea class="form-control" id="toolParams" rows="3">{"query": "AI news", "max_results": 5}</textarea>
                                    </div>
                                    <button class="btn btn-success" onclick="executeTool()">
                                        <i class="fas fa-play me-1"></i>
                                        Execute Tool
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Tool Response</h6>
                                </div>
                                <div class="card-body">
                                    <div class="response-area" id="toolResponse">Tool response will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Available Tools</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-outline-info" onclick="getAvailableTools()">
                                        <i class="fas fa-list me-1"></i>
                                        Get Available Tools
                                    </button>
                                    <div class="response-area mt-3" id="toolsList">Tools list will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agents Section -->
                <div class="api-section" id="agents">
                    <h4><i class="fas fa-robot me-2"></i>Agent Management</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Agent Operations</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-primary mb-2 w-100" onclick="getAllAgents()">
                                        <i class="fas fa-list me-1"></i>
                                        Get All Agents
                                    </button>
                                    <button class="btn btn-outline-primary mb-2 w-100" onclick="getAgentCount()">
                                        <i class="fas fa-calculator me-1"></i>
                                        Get Agent Count
                                    </button>
                                    <button class="btn btn-outline-success mb-2 w-100" onclick="refreshAgents()">
                                        <i class="fas fa-sync me-1"></i>
                                        Refresh Agents
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Agent Response</h6>
                                </div>
                                <div class="card-body">
                                    <div class="response-area" id="agentResponse">Agent response will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Models Section -->
                <div class="api-section" id="models">
                    <h4><i class="fas fa-brain me-2"></i>Model Management</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Model Operations</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-primary mb-2 w-100" onclick="getAvailableModels()">
                                        <i class="fas fa-list me-1"></i>
                                        Get Available Models
                                    </button>
                                    <button class="btn btn-outline-primary mb-2 w-100" onclick="getModelInfo()">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Get Model Info
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">Model Response</h6>
                                </div>
                                <div class="card-body">
                                    <div class="response-area" id="modelResponse">Model response will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Health Section -->
                <div class="api-section" id="health">
                    <h4><i class="fas fa-heartbeat me-2"></i>Health & Metrics</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">System Status</h6>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-success mb-2 w-100" onclick="getHealth()">
                                        <i class="fas fa-heartbeat me-1"></i>
                                        Health Check
                                    </button>
                                    <button class="btn btn-outline-info mb-2 w-100" onclick="getMetrics()">
                                        <i class="fas fa-chart-line me-1"></i>
                                        Get Metrics
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title mb-0">System Response</h6>
                                </div>
                                <div class="card-body">
                                    <div class="response-area" id="healthResponse">Health response will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuration
        let baseUrl = 'http://localhost:8080';
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            generateSessionId();
            updateBaseUrl();
            checkHealth();
        });

        // Utility functions
        function updateBaseUrl() {
            baseUrl = document.getElementById('baseUrl').value;
        }

        function generateSessionId() {
            const sessionId = 'session-' + Math.random().toString(36).substr(2, 9);
            document.getElementById('sessionId').value = sessionId;
        }

        function getUserId() {
            return document.getElementById('userId').value;
        }

        function getSessionId() {
            return document.getElementById('sessionId').value;
        }

        function updateStatusIndicator(online) {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            if (online) {
                indicator.className = 'status-indicator status-online';
                text.textContent = 'Online';
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = 'Offline';
            }
        }

        function displayResponse(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        function displayError(elementId, error) {
            const element = document.getElementById(elementId);
            element.textContent = 'Error: ' + error.message;
        }

        // API Functions
        async function makeRequest(url, options = {}) {
            try {
                updateBaseUrl();
                const response = await fetch(baseUrl + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('Request failed:', error);
                throw error;
            }
        }

        // Health check
        async function checkHealth() {
            try {
                await makeRequest('/api/v1/health');
                updateStatusIndicator(true);
            } catch (error) {
                updateStatusIndicator(false);
            }
        }

        // Conversation functions
        async function sendMessage() {
            try {
                const data = {
                    systemMessage: document.getElementById('systemMessage').value,
                    userMessages: [document.getElementById('userMessage').value],
                    userId: getUserId(),
                    sessionId: getSessionId()
                };
                
                const agentId = document.getElementById('agentId').value;
                if (agentId) {
                    data.agentId = agentId;
                }
                
                const response = await makeRequest('/conversation/message', {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                
                displayResponse('messageResponse', response);
            } catch (error) {
                displayError('messageResponse', error);
            }
        }

        async function getContext() {
            try {
                const limit = document.getElementById('contextLimit').value;
                const url = `/conversation/${getSessionId()}/context?userId=${getUserId()}&limit=${limit}`;
                const response = await makeRequest(url);
                displayResponse('messageResponse', response);
            } catch (error) {
                displayError('messageResponse', error);
            }
        }

        async function searchMemories() {
            try {
                const query = document.getElementById('memoryQuery').value;
                const url = `/conversation/${getSessionId()}/memories?query=${encodeURIComponent(query)}&userId=${getUserId()}&limit=10`;
                const response = await makeRequest(url);
                displayResponse('messageResponse', response);
            } catch (error) {
                displayError('messageResponse', error);
            }
        }

        // Tool functions
        async function executeTool() {
            try {
                const toolName = document.getElementById('toolName').value;
                const paramsText = document.getElementById('toolParams').value;
                const parameters = JSON.parse(paramsText);
                
                const data = {
                    toolName: toolName,
                    parameters: parameters,
                    sessionId: getSessionId(),
                    userId: getUserId()
                };
                
                const response = await makeRequest('/conversation/tool/execute', {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                
                displayResponse('toolResponse', response);
            } catch (error) {
                displayError('toolResponse', error);
            }
        }

        async function getAvailableTools() {
            try {
                const response = await makeRequest('/conversation/tools');
                displayResponse('toolsList', response);
            } catch (error) {
                displayError('toolsList', error);
            }
        }

        // Agent functions
        async function getAllAgents() {
            try {
                const response = await makeRequest('/api/agents');
                displayResponse('agentResponse', response);
            } catch (error) {
                displayError('agentResponse', error);
            }
        }

        async function getAgentCount() {
            try {
                const response = await makeRequest('/api/agents/count');
                displayResponse('agentResponse', response);
            } catch (error) {
                displayError('agentResponse', error);
            }
        }

        async function refreshAgents() {
            try {
                const response = await makeRequest('/api/agents/refresh', {
                    method: 'POST'
                });
                displayResponse('agentResponse', response);
            } catch (error) {
                displayError('agentResponse', error);
            }
        }

        // Model functions
        async function getAvailableModels() {
            try {
                const response = await makeRequest('/api/v1/models');
                displayResponse('modelResponse', response);
            } catch (error) {
                displayError('modelResponse', error);
            }
        }

        async function getModelInfo() {
            try {
                const modelName = prompt('Enter model name:');
                if (modelName) {
                    const response = await makeRequest(`/api/v1/models/${modelName}`);
                    displayResponse('modelResponse', response);
                }
            } catch (error) {
                displayError('modelResponse', error);
            }
        }

        // Health functions
        async function getHealth() {
            try {
                const response = await makeRequest('/api/v1/health');
                displayResponse('healthResponse', response);
            } catch (error) {
                displayError('healthResponse', error);
            }
        }

        async function getMetrics() {
            try {
                const response = await makeRequest('/api/v1/metrics');
                displayResponse('healthResponse', response);
            } catch (error) {
                displayError('healthResponse', error);
            }
        }
    </script>
</body>
</html>

