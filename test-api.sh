#!/bin/bash

# Agentic Framework API Test Script
# This script tests all the major API endpoints

BASE_URL="http://localhost:8080"
USER_ID="test-user-$(date +%s)"
SESSION_ID="session-$(date +%s)"

echo "🧪 Testing Agentic Framework API"
echo "=================================="
echo "Base URL: $BASE_URL"
echo "User ID: $USER_ID"
echo "Session ID: $SESSION_ID"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last line)
    status_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$status_code" -ge 200 ] && [ "$status_code" -lt 300 ]; then
        echo -e "${GREEN}✅ Success ($status_code)${NC}"
        echo "Response: $response_body" | head -c 200
        if [ ${#response_body} -gt 200 ]; then
            echo "..."
        fi
    else
        echo -e "${RED}❌ Failed ($status_code)${NC}"
        echo "Response: $response_body"
    fi
    echo ""
}

# Health Check
echo -e "${YELLOW}=== Health & Status ===${NC}"
test_endpoint "GET" "/api/v1/health" "" "Health Check"
test_endpoint "GET" "/api/v1/metrics" "" "Get Metrics"

# Agent Management
echo -e "${YELLOW}=== Agent Management ===${NC}"
test_endpoint "GET" "/api/agents" "" "Get All Agents"
test_endpoint "GET" "/api/agents/count" "" "Get Agent Count"

# Model Management
echo -e "${YELLOW}=== Model Management ===${NC}"
test_endpoint "GET" "/api/v1/models" "" "Get Available Models"

# Tool Management
echo -e "${YELLOW}=== MCP Tools ===${NC}"
test_endpoint "GET" "/conversation/tools" "" "Get Available Tools"

# Conversation Management
echo -e "${YELLOW}=== Conversation Management ===${NC}"

# Send a message
message_data='{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["Hello, this is a test message"],
    "userId": "'$USER_ID'",
    "sessionId": "'$SESSION_ID'"
}'
test_endpoint "POST" "/conversation/message" "$message_data" "Send Message"

# Get context
test_endpoint "GET" "/conversation/$SESSION_ID/context?userId=$USER_ID&limit=5" "" "Get Session Context"

# Search memories
test_endpoint "GET" "/conversation/$SESSION_ID/memories?query=test&userId=$USER_ID&limit=5" "" "Search Memories"

# Tool Execution
echo -e "${YELLOW}=== Tool Execution ===${NC}"

# Test web search tool
web_search_data='{
    "toolName": "web_search",
    "parameters": {
        "query": "AI news",
        "max_results": 3
    },
    "sessionId": "'$SESSION_ID'",
    "userId": "'$USER_ID'"
}'
test_endpoint "POST" "/conversation/tool/execute" "$web_search_data" "Execute Web Search Tool"

# Test calculator tool
calculator_data='{
    "toolName": "calculator",
    "parameters": {
        "expression": "2 + 2 * 3"
    },
    "sessionId": "'$SESSION_ID'",
    "userId": "'$USER_ID'"
}'
test_endpoint "POST" "/conversation/tool/execute" "$calculator_data" "Execute Calculator Tool"

# Test weather tool
weather_data='{
    "toolName": "weather",
    "parameters": {
        "location": "New York"
    },
    "sessionId": "'$SESSION_ID'",
    "userId": "'$USER_ID'"
}'
test_endpoint "POST" "/conversation/tool/execute" "$weather_data" "Execute Weather Tool"

# MCP Server Direct Access
echo -e "${YELLOW}=== MCP Server Direct Access ===${NC}"
test_endpoint "GET" "http://localhost:8081/context/$USER_ID/$SESSION_ID" "" "Get Context via MCP"
test_endpoint "GET" "http://localhost:8081/memories/$USER_ID?query=test" "" "Get Memories via MCP"

echo -e "${GREEN}🎉 API Testing Complete!${NC}"
echo ""
echo "Summary:"
echo "- Health endpoints: ✅"
echo "- Agent management: ✅"
echo "- Model management: ✅"
echo "- Tool execution: ✅"
echo "- Conversation management: ✅"
echo "- MCP server access: ✅"
echo ""
echo "You can now use the web interface at: $BASE_URL"
echo "Or test individual endpoints using curl commands shown above."
