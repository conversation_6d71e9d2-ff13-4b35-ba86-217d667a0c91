# Agentic Framework

A unified framework for AI model interactions and context management through the Model Context Protocol (MCP). This framework provides a comprehensive API for communicating with any AI agent, managing conversations, executing tools, and maintaining context and memories.

## Features

- **Agent-Agnostic Communication**: Connect to any AI agent (Claude, GPT, Llama, custom agents) through MCP
- **Context & Memory Management**: Persistent storage and retrieval of conversation context and memories
- **MCP Tool Integration**: Execute tools through the official MCP protocol
- **Real-time Communication**: WebSocket support for live agent interactions
- **Session Management**: Track and manage user sessions with timeout handling
- **RESTful API**: Complete HTTP API for all operations
- **Multi-Agent Support**: Automatic agent selection and routing

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6+
- Access to AI model APIs (OpenAI, Anthropic, etc.)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd agentic-framework
```

2. Build the project:
```bash
mvn clean install
```

3. Start the application:
```bash
./start.sh
# or
java -jar target/agentic-framework-1.0.0.jar
```

The application will start on `http://localhost:8080`

## API Endpoints

### Base URL
```
http://localhost:8080
```

### Conversation Management

#### Send Message to Agent
```http
POST /conversation/message
```

**Request Body:**
```json
{
  "systemMessage": "You are a helpful AI assistant",
  "userMessages": ["Hello, how are you?"],
  "agentId": "claude-3-sonnet", // Optional - auto-select if not provided
  "userId": "user123",
  "sessionId": "session456", // Optional - auto-generated if not provided
  "parameters": {
    "temperature": 0.7,
    "maxTokens": 1000
  }
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:8080/conversation/message \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["Hello, how are you?"],
    "userId": "user123"
  }'
```

**Response:**
```json
{
  "success": true,
  "response": {
    "response": "Hello! I'm doing well, thank you for asking. How can I help you today?",
    "modelName": "claude-3-sonnet",
    "status": "SUCCESS"
  },
  "sessionId": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": 1703123456789
}
```

#### Get Session Context
```http
GET /conversation/{sessionId}/context?userId={userId}&limit={limit}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:8080/conversation/550e8400-e29b-41d4-a716-446655440000/context?userId=user123&limit=10"
```

**Response:**
```json
{
  "success": true,
  "context": [
    {
      "timestamp": "2023-12-21T10:30:00",
      "request": {
        "systemMessage": "You are a helpful AI assistant",
        "userMessages": ["Hello, how are you?"],
        "modelName": "claude-3-sonnet"
      },
      "response": {
        "response": "Hello! I'm doing well, thank you for asking.",
        "status": "SUCCESS"
      }
    }
  ],
  "sessionId": "550e8400-e29b-41d4-a716-446655440000",
  "count": 1
}
```

#### Search Memories
```http
GET /conversation/{sessionId}/memories?query={query}&userId={userId}&limit={limit}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:8080/conversation/550e8400-e29b-41d4-a716-446655440000/memories?query=hello&userId=user123&limit=5"
```

#### Clear Session Context
```http
DELETE /conversation/{sessionId}/context?userId={userId}
```

**cURL Example:**
```bash
curl -X DELETE "http://localhost:8080/conversation/550e8400-e29b-41d4-a716-446655440000/context?userId=user123"
```

### MCP Tool Execution

#### Execute Tool
```http
POST /conversation/tool/execute
```

**Request Body:**
```json
{
  "toolName": "web_search",
  "parameters": {
    "query": "latest AI developments",
    "max_results": 5
  },
  "sessionId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "user123"
}
```

**cURL Example:**
```bash
curl -X POST http://localhost:8080/conversation/tool/execute \
  -H "Content-Type: application/json" \
  -d '{
    "toolName": "web_search",
    "parameters": {
      "query": "latest AI developments",
      "max_results": 5
    },
    "sessionId": "550e8400-e29b-41d4-a716-446655440000",
    "userId": "user123"
  }'
```

**Response:**
```json
{
  "success": true,
  "toolName": "web_search",
  "result": {
    "results": [
      {
        "title": "Latest AI Developments 2024",
        "url": "https://example.com/ai-news-1"
      },
      {
        "title": "New AI Models Released",
        "url": "https://example.com/ai-news-2"
      }
    ],
    "totalResults": 2,
    "toolName": "web_search",
    "executionTime": 1703123456789
  },
  "sessionId": "550e8400-e29b-41d4-a716-446655440000",
  "timestamp": 1703123456789
}
```

#### Get Available Tools
```http
GET /conversation/tools
```

**cURL Example:**
```bash
curl -X GET http://localhost:8080/conversation/tools
```

**Response:**
```json
{
  "success": true,
  "tools": [
    {
      "name": "web_search",
      "description": "Search the web for information",
      "parameters": {
        "query": "string",
        "max_results": "number"
      },
      "returnType": "object"
    },
    {
      "name": "file_read",
      "description": "Read contents of a file",
      "parameters": {
        "filepath": "string"
      },
      "returnType": "object"
    }
  ],
  "count": 7
}
```

### Agent Management

#### Get All Agents
```http
GET /api/agents
```

**cURL Example:**
```bash
curl -X GET http://localhost:8080/api/agents
```

#### Process Request with Specific Agent
```http
POST /api/agents/{agentId}/process
```

**cURL Example:**
```bash
curl -X POST http://localhost:8080/api/agents/claude-3-sonnet/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["What is the weather like?"],
    "userId": "user123"
  }'
```

#### Find Suitable Agents
```http
POST /api/agents/find
```

**cURL Example:**
```bash
curl -X POST http://localhost:8080/api/agents/find \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["I need help with coding"],
    "userId": "user123"
  }'
```

### Model Management

#### Get Available Models
```http
GET /api/v1/models
```

**cURL Example:**
```bash
curl -X GET http://localhost:8080/api/v1/models
```

#### Process Model Request
```http
POST /api/v1/models/process
```

**cURL Example:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant",
    "userMessages": ["Hello"],
    "modelName": "claude-3-sonnet",
    "userId": "user123"
  }'
```

### Health and Metrics

#### Health Check
```http
GET /api/v1/health
```

**cURL Example:**
```bash
curl -X GET http://localhost:8080/api/v1/health
```

#### Get Metrics
```http
GET /api/v1/metrics
```

**cURL Example:**
```bash
curl -X GET http://localhost:8080/api/v1/metrics
```

## WebSocket API

### Connection
Connect to WebSocket endpoint:
```
ws://localhost:8080/ws
```

### Message Format

#### Send Agent Message
```json
{
  "type": "agent_message",
  "systemMessage": "You are a helpful AI assistant",
  "userMessages": ["Hello"],
  "agentId": "claude-3-sonnet",
  "userId": "user123",
  "sessionId": "550e8400-e29b-41d4-a716-446655440000"
}
```

#### Send Tool Execution Request
```json
{
  "type": "tool_execution",
  "toolName": "web_search",
  "parameters": {
    "query": "AI news"
  },
  "sessionId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "user123"
}
```

### WebSocket Topics

- `/topic/agent/response` - Agent responses
- `/topic/tool/response` - Tool execution results
- `/topic/context/response` - Context retrieval results
- `/user/{userId}/private` - User-specific messages

## MCP Server Access

### MCP Server URL
```
http://localhost:8081
```

### Viewing Context and Memories

#### Direct MCP Server Access
The MCP server runs on port 8081 and provides direct access to context and memories:

**Get Context via MCP:**
```bash
curl -X GET "http://localhost:8081/context/{userId}/{sessionId}"
```

**Get Memories via MCP:**
```bash
curl -X GET "http://localhost:8081/memories/{userId}?query={searchTerm}"
```

#### MCP Server Endpoints

- `GET /context/{userId}/{sessionId}` - Get session context
- `GET /memories/{userId}` - Get user memories
- `POST /context/{userId}/{sessionId}` - Add context entry
- `DELETE /context/{userId}/{sessionId}` - Clear session context
- `GET /tools` - Get available MCP tools
- `POST /tools/{toolName}/execute` - Execute MCP tool

### MCP Server Configuration

The MCP server configuration is in `application.yml`:

```yaml
mcp:
  server:
    host: localhost
    port: 8081
    max-connections: 100
    read-timeout: 30000
    write-timeout: 30000
    enable-ssl: false
```

## Available Tools

The framework includes several built-in MCP tools:

### File Operations
- `file_read` - Read file contents
- `file_write` - Write content to file

### Web Operations
- `web_search` - Search the web
- `http_request` - Make HTTP requests

### Utility Tools
- `calculator` - Evaluate mathematical expressions
- `weather` - Get weather information
- `time_converter` - Convert time between timezones

## Configuration

### Application Properties

Key configuration options in `application.yml`:

```yaml
# Server Configuration
server:
  port: 8080

# MCP Server Configuration
mcp:
  server:
    port: 8081
    max-connections: 100

# Context Management
context:
  storage:
    max-contexts-per-user: 100
    max-context-size: 10000
  memory:
    max-memories-per-user: 1000
    memory-ttl: 86400000

# Session Management
session:
  timeout: 3600000
  max-sessions-per-user: 10

# Model Providers
mcp:
  models:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: https://api.openai.com/v1
    anthropic:
      api-key: ${ANTHROPIC_API_KEY}
      base-url: https://api.anthropic.com/v1
    local:
      base-url: http://localhost:11434
```

### Environment Variables

Set these environment variables for API keys:

```bash
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
```

## Development

### Building from Source

```bash
# Clone repository
git clone <repository-url>
cd agentic-framework

# Build with Maven
mvn clean install

# Run tests
mvn test

# Start application
mvn spring-boot:run
```

### Project Structure

```
src/main/java/com/agentic/framework/
├── agent/           # Agent implementations
├── controller/      # REST and WebSocket controllers
├── service/         # Business logic services
├── mcp/            # MCP server implementation
├── model/          # Data transfer objects
└── config/         # Configuration classes
```

### Testing

Run the test suite:
```bash
mvn test
```

Test specific endpoints:
```bash
# Test API endpoints
./test-api.sh

# Test agent functionality
./test-agents.sh

# View contexts
./view-contexts.sh
```

## Troubleshooting

### Common Issues

1. **MCP Server Connection Failed**
   - Check if MCP server is running on port 8081
   - Verify firewall settings
   - Check MCP server logs

2. **API Key Issues**
   - Ensure environment variables are set correctly
   - Verify API key permissions
   - Check API provider status

3. **WebSocket Connection Issues**
   - Ensure WebSocket endpoint is accessible
   - Check CORS configuration
   - Verify client WebSocket implementation

### Logs

Application logs are written to:
```
logs/agentic-framework.log
```

### Health Checks

Monitor application health:
```bash
curl http://localhost:8080/actuator/health
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs for error details
