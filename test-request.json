{"conversation": {"send_message": {"description": "Send a message to an agent", "method": "POST", "url": "http://localhost:8080/conversation/message", "headers": {"Content-Type": "application/json"}, "body": {"systemMessage": "You are a helpful AI assistant", "userMessages": ["Hello, how are you today?"], "userId": "test-user-123", "sessionId": "session-456", "parameters": {"temperature": 0.7, "maxTokens": 1000}}}, "get_context": {"description": "Get conversation context for a session", "method": "GET", "url": "http://localhost:8080/conversation/session-456/context?userId=test-user-123&limit=10"}, "search_memories": {"description": "Search memories for a session", "method": "GET", "url": "http://localhost:8080/conversation/session-456/memories?query=hello&userId=test-user-123&limit=5"}, "clear_context": {"description": "Clear session context", "method": "DELETE", "url": "http://localhost:8080/conversation/session-456/context?userId=test-user-123"}}, "tools": {"execute_tool": {"description": "Execute an MCP tool", "method": "POST", "url": "http://localhost:8080/conversation/tool/execute", "headers": {"Content-Type": "application/json"}, "body": {"toolName": "web_search", "parameters": {"query": "latest AI developments", "max_results": 5}, "sessionId": "session-456", "userId": "test-user-123"}}, "get_tools": {"description": "Get available MCP tools", "method": "GET", "url": "http://localhost:8080/conversation/tools"}}, "agents": {"get_all_agents": {"description": "Get all available agents", "method": "GET", "url": "http://localhost:8080/api/agents"}, "get_agent_count": {"description": "Get agent count", "method": "GET", "url": "http://localhost:8080/api/agents/count"}, "process_with_agent": {"description": "Process request with specific agent", "method": "POST", "url": "http://localhost:8080/api/agents/general-purpose/process", "headers": {"Content-Type": "application/json"}, "body": {"systemMessage": "You are a helpful AI assistant", "userMessages": ["What is artificial intelligence?"], "userId": "test-user-123", "sessionId": "session-456"}}, "find_agents": {"description": "Find agents for a specific request", "method": "POST", "url": "http://localhost:8080/api/agents/find", "headers": {"Content-Type": "application/json"}, "body": {"systemMessage": "You are a helpful AI assistant", "userMessages": ["I need help with coding"], "userId": "test-user-123"}}, "refresh_agents": {"description": "Refresh agent registry", "method": "POST", "url": "http://localhost:8080/api/agents/refresh"}}, "models": {"get_models": {"description": "Get available models", "method": "GET", "url": "http://localhost:8080/api/v1/models"}, "get_model_info": {"description": "Get specific model information", "method": "GET", "url": "http://localhost:8080/api/v1/models/claude-3-sonnet"}, "process_model_request": {"description": "Process request with specific model", "method": "POST", "url": "http://localhost:8080/api/v1/models/process", "headers": {"Content-Type": "application/json"}, "body": {"systemMessage": "You are a helpful AI assistant", "userMessages": ["Hello"], "modelName": "claude-3-sonnet", "userId": "test-user-123", "sessionId": "session-456"}}}, "health": {"health_check": {"description": "Health check", "method": "GET", "url": "http://localhost:8080/api/v1/health"}, "get_metrics": {"description": "Get system metrics", "method": "GET", "url": "http://localhost:8080/api/v1/metrics"}}, "mcp_server": {"get_context": {"description": "Get context via MCP server", "method": "GET", "url": "http://localhost:8081/context/test-user-123/session-456"}, "get_memories": {"description": "Get memories via MCP server", "method": "GET", "url": "http://localhost:8081/memories/test-user-123?query=hello"}, "get_tools": {"description": "Get tools via MCP server", "method": "GET", "url": "http://localhost:8081/tools"}}, "examples": {"curl_examples": {"send_message": "curl -X POST http://localhost:8080/conversation/message -H 'Content-Type: application/json' -d '{\"systemMessage\":\"You are a helpful AI assistant\",\"userMessages\":[\"Hello\"],\"userId\":\"test-user-123\",\"sessionId\":\"session-456\"}'", "get_context": "curl -X GET 'http://localhost:8080/conversation/session-456/context?userId=test-user-123&limit=10'", "execute_tool": "curl -X POST http://localhost:8080/conversation/tool/execute -H 'Content-Type: application/json' -d '{\"toolName\":\"web_search\",\"parameters\":{\"query\":\"AI news\"},\"sessionId\":\"session-456\",\"userId\":\"test-user-123\"}'", "get_agents": "curl -X GET http://localhost:8080/api/agents", "health_check": "curl -X GET http://localhost:8080/api/v1/health"}}}