@echo off
REM Agentic Framework Startup Script for Windows
REM This script starts the Agentic Framework application

echo 🚀 Starting Agentic Framework
echo =============================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java 17 or higher and try again.
    pause
    exit /b 1
)

REM Check Java version
for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
    goto :found_java
)
:found_java

echo ✅ Java found: %JAVA_VERSION%

REM Check if <PERSON><PERSON> is installed
mvn -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Maven is not installed or not in PATH
    echo Please install Maven and try again.
    pause
    exit /b 1
)

echo ✅ Maven found

REM Check if the application is already running
curl -s http://localhost:8080/api/v1/health >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  Application is already running on port 8080
    echo You can access it at: http://localhost:8080
    echo.
    echo To stop the application, close this window
    echo To restart, first stop the current instance and run this script again.
    pause
    exit /b 0
)

REM Build the application
echo 🔨 Building application...
call mvn clean install -DskipTests
if errorlevel 1 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build successful

REM Set environment variables (optional)
if not defined OPENAI_API_KEY (
    echo ⚠️  Warning: OPENAI_API_KEY not set
    echo For full functionality, set the environment variable:
    echo   set OPENAI_API_KEY=your-openai-api-key
)

if not defined ANTHROPIC_API_KEY (
    echo ⚠️  Warning: ANTHROPIC_API_KEY not set
    echo For full functionality, set the environment variable:
    echo   set ANTHROPIC_API_KEY=your-anthropic-api-key
)

echo.
echo The application will still start but some features may be limited.

REM Start the application
echo 🚀 Starting Agentic Framework...
echo.

REM Start the application
start /B java -jar target/agentic-framework-1.0.0.jar > logs\startup.log 2>&1

REM Wait for the application to start
echo ⏳ Waiting for application to start...
for /l %%i in (1,1,30) do (
    curl -s http://localhost:8080/api/v1/health >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Application started successfully!
        goto :app_started
    )
    echo -n .
    timeout /t 1 /nobreak >nul
)

echo ❌ Application failed to start within 30 seconds
echo Check the logs at logs\startup.log for more information.
pause
exit /b 1

:app_started
echo.
echo 🎉 Agentic Framework is now running!
echo.
echo 📋 Access Information:
echo   • Web Interface: http://localhost:8080
echo   • REST API: http://localhost:8080/api/v1
echo   • MCP Server: http://localhost:8081
echo   • Health Check: http://localhost:8080/api/v1/health
echo.
echo 🛠️  Available Scripts:
echo   • Test API: test-api.sh (Linux/Mac) or test-api.bat (Windows)
echo   • Test Agents: test-agents.sh (Linux/Mac) or test-agents.bat (Windows)
echo   • View Contexts: view-contexts.sh (Linux/Mac) or view-contexts.bat (Windows)
echo.
echo 📚 Documentation:
echo   • README.md - Complete API documentation
echo   • API Examples - See README.md for curl examples
echo.
echo 🔄 To stop the application:
echo   • Close this window
echo   • Or run: taskkill /f /im java.exe
echo.
echo 📊 Application Status:
curl -s http://localhost:8080/api/v1/health

echo.
echo 💡 Tip: Open http://localhost:8080 in your browser for the web interface!
echo.
echo Press any key to stop the application...
pause >nul

echo 🛑 Shutting down Agentic Framework...
taskkill /f /im java.exe >nul 2>&1
echo ✅ Application stopped
pause
