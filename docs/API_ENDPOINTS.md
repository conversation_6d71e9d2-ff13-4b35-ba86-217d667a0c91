# Agentic Framework API Documentation

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication
Currently, the API is configured without authentication for development purposes.

## API Endpoints

### 1. Process Model Request (Ask Questions)
**Endpoint:** `POST /models/process`

**Description:** Send a request to any available AI model with system message, user messages, and model name.

**Curl Command:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant. Provide clear and concise answers.",
    "userMessages": ["What is the capital of France?"],
    "modelName": "gpt-4",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

**Example with different models:**

**OpenAI GPT-4:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["Explain quantum computing in simple terms"],
    "modelName": "gpt-4",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

**OpenAI GPT-3.5:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["What are the benefits of exercise?"],
    "modelName": "gpt-3.5-turbo",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

**Anthropic Claude 3 Sonnet:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["Write a short poem about technology"],
    "modelName": "claude-3-sonnet",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

**Anthropic Claude 3 Haiku:**
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant.",
    "userMessages": ["What is machine learning?"],
    "modelName": "claude-3-haiku",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

### 2. Get Available Models
**Endpoint:** `GET /models`

**Description:** Retrieve a list of all available AI models and their status.

**Curl Command:**
```bash
curl -X GET http://localhost:8080/api/v1/models
```

### 3. Get Model Information
**Endpoint:** `GET /models/{modelName}`

**Description:** Get detailed information about a specific model.

**Curl Commands:**
```bash
# Get GPT-4 info
curl -X GET http://localhost:8080/api/v1/models/gpt-4

# Get Claude 3 Sonnet info
curl -X GET http://localhost:8080/api/v1/models/claude-3-sonnet

# Get GPT-3.5 info
curl -X GET http://localhost:8080/api/v1/models/gpt-3.5-turbo
```

### 4. Get User Context
**Endpoint:** `GET /context/{userId}`

**Description:** Retrieve conversation context for a specific user and session.

**Curl Commands:**
```bash
# Get context for user with session
curl -X GET "http://localhost:8080/api/v1/context/user123?sessionId=session456&limit=10"

# Get context without session (all sessions)
curl -X GET "http://localhost:8080/api/v1/context/user123?limit=20"
```

### 5. Clear User Session
**Endpoint:** `DELETE /context/{userId}/session/{sessionId}`

**Description:** Clear conversation context for a specific user session.

**Curl Command:**
```bash
curl -X DELETE http://localhost:8080/api/v1/context/user123/session/session456
```

### 6. Clear All User Data
**Endpoint:** `DELETE /context/{userId}`

**Description:** Clear all data and context for a specific user.

**Curl Command:**
```bash
curl -X DELETE http://localhost:8080/api/v1/context/user123
```

### 7. Search User Memories
**Endpoint:** `GET /context/{userId}/memories`

**Description:** Search through user's conversation history and memories.

**Curl Commands:**
```bash
# Search for conversations about AI
curl -X GET "http://localhost:8080/api/v1/context/user123/memories?query=AI&limit=10"

# Search for conversations about programming
curl -X GET "http://localhost:8080/api/v1/context/user123/memories?query=programming&limit=15"
```

### 8. Health Check
**Endpoint:** `GET /health`

**Description:** Check the health status of the server.

**Curl Command:**
```bash
curl -X GET http://localhost:8080/api/v1/health
```

### 9. Server Metrics
**Endpoint:** `GET /metrics`

**Description:** Get server performance metrics.

**Curl Command:**
```bash
curl -X GET http://localhost:8080/api/v1/metrics
```

## Request Body Schema

### ModelRequest
```json
{
  "systemMessage": "string (required)",
  "userMessages": ["string"] (required),
  "modelName": "string (required)",
  "userId": "string (required)",
  "sessionId": "string (required)",
  "config": {
    "temperature": 0.7,
    "maxTokens": 4000,
    "topP": 1.0,
    "stream": false
  }
}
```

## Response Schema

### ModelResponse
```json
{
  "modelName": "string",
  "response": "string",
  "status": "SUCCESS|ERROR",
  "responseId": "string",
  "userId": "string",
  "sessionId": "string",
  "tokensUsed": 150,
  "metadata": {
    "model": "string",
    "provider": "string",
    "requestId": "string"
  }
}
```

## Example Usage Scenarios

### 1. Start a New Conversation
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant. Be friendly and informative.",
    "userMessages": ["Hello! Can you help me learn about artificial intelligence?"],
    "modelName": "gpt-4",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

### 2. Continue a Conversation
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant. Be friendly and informative.",
    "userMessages": ["That was helpful! Can you give me some examples of AI applications?"],
    "modelName": "gpt-4",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

### 3. Switch Models
```bash
curl -X POST http://localhost:8080/api/v1/models/process \
  -H "Content-Type: application/json" \
  -d '{
    "systemMessage": "You are a helpful AI assistant. Be friendly and informative.",
    "userMessages": ["Now let me ask Claude about this topic"],
    "modelName": "claude-3-sonnet",
    "userId": "user123",
    "sessionId": "session456"
  }'
```

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Request successful
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Model or resource not found
- `500 Internal Server Error`: Server error

## Rate Limiting

Currently, no rate limiting is implemented for development purposes.

## Notes

- The `userId` and `sessionId` are used to maintain conversation context
- All conversations are stored in memory and persist across requests
- The system automatically manages context cleanup and memory optimization
- Model responses include metadata about token usage and provider information
