# MCP (Model Context Protocol) Server Implementation

## Overview

The Agentic Framework now includes a proper MCP (Model Context Protocol) server implementation that follows the official MCP specification. This replaces the previous custom socket-based implementation with a standardized protocol for model and agent interactions.

## Features

### ✅ **Official MCP Protocol Support**
- JSON-RPC 2.0 message format
- Proper MCP method handling
- Standardized error responses
- Protocol version compliance

### ✅ **Agent Integration**
- Dynamic agent discovery and listing
- Agent calling with proper parameter handling
- Agent capabilities exposure
- Priority-based agent routing

### ✅ **Model Integration**
- Model provider registry
- Model calling with standardized interface
- Support for multiple model providers (OpenAI, Anthropic, Local)
- Model configuration management

### ✅ **Tools Support**
- Tools listing and discovery
- Tool calling with proper argument handling
- Agent-based tools integration

## Architecture

### Server Components

1. **SimpleMcpServer** - Main MCP server implementation
   - Socket-based communication
   - JSON-RPC 2.0 protocol handling
   - Client connection management

2. **McpClientHandler** - Client request processing
   - Message parsing and validation
   - Method routing
   - Response formatting

3. **AgentRegistry** - Agent management
   - Agent registration and discovery
   - Capability-based routing
   - Priority handling

4. **ModelRegistry** - Model management
   - Model provider registration
   - Model configuration
   - Provider routing

## MCP Methods Supported

### Core Methods
- `initialize` - Server initialization and capability discovery
- `tools/list` - List available tools
- `tools/call` - Execute a tool
- `agents/list` - List available agents
- `agents/call` - Call an agent
- `models/list` - List available models
- `models/call` - Call a model

### Message Format

#### Request Format
```json
{
  "jsonrpc": "2.0",
  "method": "agents/call",
  "id": "unique-request-id",
  "params": {
    "name": "agent-id",
    "arguments": {
      "prompt": "Your request here"
    }
  }
}
```

#### Response Format
```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id",
  "result": {
    "content": "Agent response",
    "agentId": "agent-id",
    "agentName": "Agent Name"
  }
}
```

#### Error Format
```json
{
  "jsonrpc": "2.0",
  "id": "unique-request-id",
  "error": {
    "code": "error_code",
    "message": "Error description"
  }
}
```

## Usage

### Starting the MCP Server

The MCP server starts automatically with the Spring Boot application on port 8081.

```bash
# Start the application
./mvnw spring-boot:run
```

### Connecting to the MCP Server

```java
// Connect to the MCP server
Socket socket = new Socket("localhost", 8081);
BufferedReader reader = new BufferedReader(new InputStreamReader(socket.getInputStream()));
PrintWriter writer = new PrintWriter(socket.getOutputStream(), true);

// Send initialization message
JsonObject initRequest = new JsonObject();
initRequest.addProperty("jsonrpc", "2.0");
initRequest.addProperty("method", "initialize");
initRequest.addProperty("id", UUID.randomUUID().toString());
writer.println(initRequest.toString());

// Read initialization response
String initResponse = reader.readLine();
```

### Example: Calling an Agent

```java
// Create agent call request
JsonObject request = new JsonObject();
request.addProperty("jsonrpc", "2.0");
request.addProperty("method", "agents/call");
request.addProperty("id", UUID.randomUUID().toString());

JsonObject params = new JsonObject();
params.addProperty("name", "time-series-forecasting-agent");

JsonObject arguments = new JsonObject();
arguments.addProperty("prompt", "Analyze this time series data: [1, 2, 3, 4, 5]");
params.add("arguments", arguments);

request.add("params", params);

// Send request
writer.println(request.toString());

// Read response
String response = reader.readLine();
```

### Example: Listing Available Agents

```java
// Create agents list request
JsonObject request = new JsonObject();
request.addProperty("jsonrpc", "2.0");
request.addProperty("method", "agents/list");
request.addProperty("id", UUID.randomUUID().toString());

// Send request
writer.println(request.toString());

// Read response
String response = reader.readLine();
```

## Available Agents

The MCP server exposes the following agents:

1. **Time Series Forecasting Agent** (`time-series-forecasting-agent`)
   - Capabilities: Time series analysis, forecasting, trend prediction
   - Priority: High for time series related requests

2. **Software Bug Assistant Agent** (`software-bug-assistant-agent`)
   - Capabilities: Bug analysis, code debugging, error diagnosis
   - Priority: High for software debugging requests

3. **Data Analysis Agent** (`data-analysis-agent`)
   - Capabilities: Data analysis, statistical modeling, insights
   - Priority: Medium for data analysis requests

4. **Code Review Agent** (`code-review-agent`)
   - Capabilities: Code review, best practices, quality assessment
   - Priority: Medium for code review requests

5. **General Purpose Agent** (`general-purpose-agent`)
   - Capabilities: General assistance, conversation, information
   - Priority: Low (fallback agent)

## Available Models

The MCP server supports multiple model providers:

### OpenAI Models
- `gpt-5`, `gpt-4o`, `gpt-4.1`, `gpt-4.1-mini`, `gpt-3.5`

### Anthropic Models
- `claude-3-5-sonnet-20241022`, `claude-3-5-haiku-20241022`
- `claude-3-opus-latest`, `claude-3-sonnet-20240229`
- `claude-3-haiku-20240307`, `claude-opus-4-1-20250805`

### Local Models
- `llama2`, `mistral`

## Configuration

### Server Configuration

```yaml
# application.yml
mcp:
  server:
    port: 8081
    max-connections: 10
    read-timeout: 30000
    write-timeout: 30000
```

### Model Configuration

```yaml
mcp:
  models:
    openai:
      api-key: ${OPENAI_API_KEY}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
    anthropic:
      api-key: ${ANTHROPIC_API_KEY}
      base-url: ${ANTHROPIC_BASE_URL:https://api.anthropic.com}
    local:
      base-url: ${LOCAL_MODEL_URL:http://localhost:11434}
```

## Testing

The framework includes an automated MCP client test (`McpClientTest`) that demonstrates:

1. Connecting to the MCP server
2. Listing available agents
3. Calling agents with requests
4. Listing available models
5. Listing available tools

Run the test by starting the application:

```bash
./mvnw spring-boot:run
```

The test will automatically run and log the results.

## Migration from Custom Implementation

The old custom socket-based implementation (`AgenticMcpServer`) has been disabled in favor of the new `SimpleMcpServer`. The new implementation provides:

- ✅ Standard MCP protocol compliance
- ✅ Better error handling
- ✅ Proper JSON-RPC 2.0 formatting
- ✅ Enhanced agent and model integration
- ✅ Improved client communication

## Future Enhancements

1. **WebSocket Support** - Add WebSocket transport for real-time communication
2. **Authentication** - Implement MCP authentication mechanisms
3. **Streaming** - Support for streaming responses
4. **Plugins** - Dynamic plugin loading for additional capabilities
5. **Monitoring** - Enhanced monitoring and metrics

## References

- [MCP Specification](https://modelcontextprotocol.io/)
- [JSON-RPC 2.0 Specification](https://www.jsonrpc.org/specification)
- [Google ADK Samples](https://github.com/google/adk-samples)
